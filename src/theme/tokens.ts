import { PaletteColorOptions } from '@mui/material/styles';

// Event type color mappings for both light and dark themes
export const eventTypePalette = {
  light: {
    receiving: { main: '#10b981', light: '#d1fae5', dark: '#047857', contrastText: '#ffffff' },
    sales: { main: '#3b82f6', light: '#dbeafe', dark: '#1d4ed8', contrastText: '#ffffff' },
    disposal: { main: '#ef4444', light: '#fee2e2', dark: '#dc2626', contrastText: '#ffffff' },
    transfer: { main: '#f59e0b', light: '#fef3c7', dark: '#d97706', contrastText: '#ffffff' },
    adjustment: { main: '#8b5cf6', light: '#ede9fe', dark: '#7c3aed', contrastText: '#ffffff' },
    temperature: { main: '#06b6d4', light: '#cffafe', dark: '#0891b2', contrastText: '#ffffff' },
    cleaning: { main: '#84cc16', light: '#ecfccb', dark: '#65a30d', contrastText: '#ffffff' },
    maintenance: { main: '#f97316', light: '#fed7aa', dark: '#ea580c', contrastText: '#ffffff' },
    inspection: { main: '#ec4899', light: '#fce7f3', dark: '#db2777', contrastText: '#ffffff' },
  },
  dark: {
    receiving: { main: '#34d399', light: '#10b981', dark: '#064e3b', contrastText: '#000000' },
    sales: { main: '#60a5fa', light: '#3b82f6', dark: '#1e3a8a', contrastText: '#000000' },
    disposal: { main: '#f87171', light: '#ef4444', dark: '#7f1d1d', contrastText: '#000000' },
    transfer: { main: '#fbbf24', light: '#f59e0b', dark: '#78350f', contrastText: '#000000' },
    adjustment: { main: '#a78bfa', light: '#8b5cf6', dark: '#4c1d95', contrastText: '#000000' },
    temperature: { main: '#22d3ee', light: '#06b6d4', dark: '#164e63', contrastText: '#000000' },
    cleaning: { main: '#a3e635', light: '#84cc16', dark: '#365314', contrastText: '#000000' },
    maintenance: { main: '#fb923c', light: '#f97316', dark: '#7c2d12', contrastText: '#000000' },
    inspection: { main: '#f472b6', light: '#ec4899', dark: '#831843', contrastText: '#000000' },
  },
} as const;

// Semantic surface colors for consistent theming
export const semanticSurfaces = {
  light: {
    // Primary surfaces
    primary: '#ffffff',
    secondary: '#f7f7fb',
    tertiary: '#eef0f4',
    
    // Interactive surfaces
    hover: '#f3f4f6',
    active: '#e5e7eb',
    selected: '#dbeafe',
    
    // Status surfaces
    success: '#d1fae5',
    warning: '#fef3c7',
    error: '#fee2e2',
    info: '#dbeafe',
    
    // Elevated surfaces
    elevated: '#ffffff',
    modal: '#ffffff',
    dropdown: '#ffffff',
    tooltip: '#1f2937',
  },
  dark: {
    // Primary surfaces
    primary: '#0f131b',
    secondary: '#1a1f2e',
    tertiary: '#252a3a',
    
    // Interactive surfaces
    hover: '#2d3748',
    active: '#374151',
    selected: '#1e3a8a',
    
    // Status surfaces
    success: '#064e3b',
    warning: '#78350f',
    error: '#7f1d1d',
    info: '#1e3a8a',
    
    // Elevated surfaces
    elevated: '#1a1f2e',
    modal: '#1f2937',
    dropdown: '#1f2937',
    tooltip: '#374151',
  },
} as const;

// Text contrast colors for accessibility
export const textContrasts = {
  light: {
    primary: '#0f131b',
    secondary: '#6b7280',
    tertiary: '#9ca3af',
    disabled: '#d1d5db',
    inverse: '#ffffff',
    
    // On colored backgrounds
    onPrimary: '#ffffff',
    onSecondary: '#0f131b',
    onSuccess: '#064e3b',
    onWarning: '#78350f',
    onError: '#7f1d1d',
    onInfo: '#1e3a8a',
  },
  dark: {
    primary: '#e5e7eb',
    secondary: '#98a2b3',
    tertiary: '#6b7280',
    disabled: '#4b5563',
    inverse: '#0f131b',
    
    // On colored backgrounds
    onPrimary: '#000000',
    onSecondary: '#e5e7eb',
    onSuccess: '#d1fae5',
    onWarning: '#fef3c7',
    onError: '#fee2e2',
    onInfo: '#dbeafe',
  },
} as const;

// Border colors for consistent styling
export const borderColors = {
  light: {
    default: 'rgba(15,19,27,0.08)',
    subtle: 'rgba(15,19,27,0.05)',
    strong: 'rgba(15,19,27,0.12)',
    interactive: '#d1d5db',
    focus: '#6366f1',
  },
  dark: {
    default: 'rgba(255,255,255,0.12)',
    subtle: 'rgba(255,255,255,0.08)',
    strong: 'rgba(255,255,255,0.16)',
    interactive: '#4b5563',
    focus: '#818cf8',
  },
} as const;

// Helper function to get event type colors
export const getEventTypeColors = (theme: 'light' | 'dark', eventType: keyof typeof eventTypePalette.light) => {
  return eventTypePalette[theme][eventType] || eventTypePalette[theme].receiving;
};

// Helper function to get semantic surface color
export const getSurfaceColor = (theme: 'light' | 'dark', surface: keyof typeof semanticSurfaces.light) => {
  return semanticSurfaces[theme][surface];
};

// Helper function to get text color
export const getTextColor = (theme: 'light' | 'dark', textType: keyof typeof textContrasts.light) => {
  return textContrasts[theme][textType];
};

// Helper function to get border color
export const getBorderColor = (theme: 'light' | 'dark', borderType: keyof typeof borderColors.light) => {
  return borderColors[theme][borderType];
};

// Type definitions for theme augmentation
export type EventType = keyof typeof eventTypePalette.light;
export type SurfaceType = keyof typeof semanticSurfaces.light;
export type TextType = keyof typeof textContrasts.light;
export type BorderType = keyof typeof borderColors.light;

// Export theme mode type
export type ThemeMode = 'light' | 'dark';
