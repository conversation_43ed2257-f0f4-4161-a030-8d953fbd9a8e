import * as React from 'react';
import { ThemeProvider, useColorScheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import IconButton from '@mui/material/IconButton';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { theme } from './theme';

function ModeToggle() {
  const { mode, setMode } = useColorScheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || !mode) {
    return (
      <IconButton size="small" disabled>
        <DarkModeIcon />
      </IconButton>
    );
  }

  return (
    <IconButton
      size="small"
      color="primary"
      onClick={() => {
        const newMode = mode === 'dark' ? 'light' : 'dark';
        setMode(newMode);
      }}
      aria-label="Toggle color mode"
    >
      {mode === 'dark' ? <LightModeIcon /> : <DarkModeIcon />}
    </IconButton>
  );
}

export const TopbarActions: React.FC<{ userEmail?: string }> = ({ userEmail }) => (
  <Stack direction="row" spacing={1.5} alignItems="center">
    {userEmail && (
      <Typography variant="caption" color="text.secondary" sx={{ display: { xs: 'none', sm: 'block' } }}>
        {userEmail}
      </Typography>
    )}
    <ModeToggle />
  </Stack>
);

export default function AppThemeProvider({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider theme={theme} defaultMode="light" disableTransitionOnChange>
      <CssBaseline enableColorScheme />
      {children}
    </ThemeProvider>
  );
}
