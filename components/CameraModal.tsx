import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { GoogleGenAI, Type } from '@google/genai';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Typography, Stack, Paper, TextField, Chip, Alert, CircularProgress, useTheme } from '@mui/material';
import { alpha } from '@mui/material/styles';
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import DeleteIcon from '@mui/icons-material/Delete';
import { HACCPEvent, Species, Vendor, Location } from '../types';
import { TrashIcon } from './IconComponents';

interface CameraModalProps {
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onClose: () => void;
    onSave: (events: Partial<HACCPEvent>[]) => void;
}

type ModalStep = 'camera' | 'preview' | 'loading' | 'confirm';

export const CameraModal: React.FC<CameraModalProps> = ({ species, vendors, locations, onClose, onSave }) => {
    const [step, setStep] = useState<ModalStep>('camera');
    const [capturedImage, setCapturedImage] = useState<string | null>(null);
    const [extractedEvents, setExtractedEvents] = useState<Partial<HACCPEvent>[]>([]);
    const [error, setError] = useState<string | null>(null);
    const videoRef = useRef<HTMLVideoElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const streamRef = useRef<MediaStream | null>(null);

    const stopCamera = useCallback(() => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
        }
    }, []);
    
    useEffect(() => {
        const startCamera = async () => {
            if (step === 'camera' && !streamRef.current) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } });
                    streamRef.current = stream;
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                    }
                } catch (err) {
                    console.error("Error accessing camera:", err);
                    setError("Could not access camera. Please check permissions.");
                }
            }
        };

        startCamera();
        
        return () => {
            stopCamera();
        };
    }, [step, stopCamera]);


    const handleTakePicture = () => {
        if (videoRef.current && canvasRef.current) {
            const video = videoRef.current;
            const canvas = canvasRef.current;
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const context = canvas.getContext('2d');
            if (context) {
                context.drawImage(video, 0, 0, canvas.width, canvas.height);
                const dataUrl = canvas.toDataURL('image/jpeg');
                setCapturedImage(dataUrl);
                setStep('preview');
                stopCamera();
            }
        }
    };

    const handleUsePicture = async () => {
        if (!capturedImage) return;
        setStep('loading');
        setError(null);
        try {
            const ai = new GoogleGenAI({ apiKey: import.meta.env.VITE_API_KEY as string });
            const base64Data = capturedImage.split(',')[1];
            
            const imagePart = { inlineData: { mimeType: 'image/jpeg', data: base64Data } };
            
            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash',
                contents: { parts: [imagePart] },
                config: {
                    systemInstruction: `You are an intelligent data entry assistant for a seafood company. Analyze this image of a document (e.g., an invoice, receiving log, or inventory sheet). Extract all distinct line items as separate events. Structure your response as a JSON array of objects, where each object represents one event. Use the provided schema. Extract all available columns of data, such as product name, quantity, supplier, batch number, location, temperature, product form, and unit. Map them to the fields in the provided JSON schema. Infer the 'eventType' from the document's title or content (e.g., 'receiving' for invoices, 'inventory' for stock takes). If a date is present, use it in YYYY-MM-DD format; otherwise, omit the date field. For quantities, provide a number. Be precise. If a value for a field isn't in the document, omit that key from the object. Your output MUST be ONLY the JSON array.`,
                    responseMimeType: 'application/json',
                    responseSchema: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.OBJECT,
                            properties: {
                                eventType: { type: Type.STRING, enum: ['receiving', 'inventory', 'disposal', 'sales', 'sanitation'] },
                                product: { type: Type.STRING, description: 'The name of the seafood species.' },
                                productForm: { type: Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                                quantity: { type: Type.NUMBER, description: 'The quantity of the product.' },
                                unit: { type: Type.STRING, description: 'The unit of measurement, e.g., lbs.' },
                                supplier: { type: Type.STRING, description: 'The name of the vendor or supplier.' },
                                location: { type: Type.STRING, description: 'The storage location.' },
                                date: { type: Type.STRING, description: 'The date of the event in YYYY-MM-DD format.' },
                                batchNumber: { type: Type.STRING, description: 'The batch or lot number.' },
                                temperature: { type: Type.NUMBER, description: 'The temperature in Fahrenheit.' },
                                notes: { type: Type.STRING, description: 'Any other relevant notes from the document.' },
                            },
                        },
                    },
                },
            });
            
            const parsedEvents = JSON.parse(response.text);
            setExtractedEvents(parsedEvents);
            setStep('confirm');

        } catch (err: any) {
            console.error("Error analyzing image:", err);
            if (err.message?.includes('does not have permission') || err.message?.includes('not found')) {
                setError("API Key Error. Please close and re-select your API key from the main screen.");
            } else {
                setError("AI analysis failed. Please try a clearer picture.");
            }
            setStep('preview');
        }
    };

    const handleDataChange = (index: number, field: keyof HACCPEvent, value: string | number) => {
        const updatedEvents = [...extractedEvents];
        updatedEvents[index] = { ...updatedEvents[index], [field]: value };
        setExtractedEvents(updatedEvents);
    };

    const removeEvent = (index: number) => {
        setExtractedEvents(prev => prev.filter((_, i) => i !== index));
    };

    const { newSpecies, newVendors, newLocations } = useMemo(() => {
        const existingSpecies = new Set(species.map(s => s.name.toLowerCase()));
        const existingVendors = new Set(vendors.map(v => v.name.toLowerCase()));
        const existingLocations = new Set(locations.map(l => l.name.toLowerCase()));

        const newSpecies = new Set<string>();
        const newVendors = new Set<string>();
        const newLocations = new Set<string>();

        extractedEvents.forEach(event => {
            if (event.product && !existingSpecies.has(event.product.toLowerCase())) {
                newSpecies.add(event.product);
            }
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) {
                newVendors.add(event.supplier);
            }
            if (event.location && !existingLocations.has(event.location.toLowerCase())) {
                newLocations.add(event.location);
            }
        });

        return { 
            newSpecies: Array.from(newSpecies), 
            newVendors: Array.from(newVendors), 
            newLocations: Array.from(newLocations) 
        };
    }, [extractedEvents, species, vendors, locations]);

    const theme = useTheme();
    
    const renderContent = () => {
        switch (step) {
            case 'camera':
                return (
                    <Box sx={{ position: 'relative', width: '100%', height: '100%', bgcolor: 'background.default', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                        <video ref={videoRef} autoPlay playsInline style={{ width: '100%', height: '100%', objectFit: 'contain' }} />
                        <IconButton
                            onClick={handleTakePicture}
                            sx={{
                                position: 'absolute',
                                bottom: 32,
                                bgcolor: 'background.paper',
                                p: 2,
                                '&:hover': { bgcolor: 'action.hover' },
                            }}
                        >
                            <CameraAltIcon sx={{ fontSize: 32 }} />
                        </IconButton>
                    </Box>
                );
            case 'preview':
                return (
                    <Box sx={{ position: 'relative', width: '100%', height: '100%', bgcolor: 'background.default', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                        {capturedImage && <img src={capturedImage} alt="Captured" style={{ width: '100%', height: '100%', objectFit: 'contain' }} />}
                        {error && (
                            <Alert severity="error" sx={{ position: 'absolute', top: 16, mx: 2 }}>
                                {error}
                            </Alert>
                        )}
                        <Stack direction="row" spacing={4} sx={{ position: 'absolute', bottom: 32 }}>
                            <Button onClick={() => setStep('camera')} variant="outlined" size="large">
                                Retake
                            </Button>
                            <Button onClick={handleUsePicture} variant="contained" size="large">
                                Use Picture
                            </Button>
                        </Stack>
                    </Box>
                );
            case 'loading':
                return (
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        <CircularProgress size={64} />
                        <Typography variant="h6" sx={{ mt: 2, color: 'text.primary' }}>
                            Analyzing Document...
                        </Typography>
                    </Box>
                );
            case 'confirm':
                const hasNewItems = newSpecies.length > 0 || newVendors.length > 0 || newLocations.length > 0;
                return (
                    <Box sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
                        <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 2, color: 'text.primary' }}>
                            Confirm Extracted Data
                        </Typography>
                        {hasNewItems && (
                            <Alert severity="info" sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>New items detected!</Typography>
                                <Typography variant="body2">These will be added to your database upon saving:</Typography>
                                <Box component="ul" sx={{ pl: 2, mt: 0.5, fontSize: '0.875rem' }}>
                                    {newSpecies.length > 0 && <li><strong>Species:</strong> {newSpecies.join(', ')}</li>}
                                    {newVendors.length > 0 && <li><strong>Vendors:</strong> {newVendors.join(', ')}</li>}
                                    {newLocations.length > 0 && <li><strong>Locations:</strong> {newLocations.join(', ')}</li>}
                                </Box>
                            </Alert>
                        )}
                        <Stack spacing={2}>
                            {extractedEvents.map((event, index) => {
                                const isNewProduct = event.product && newSpecies.includes(event.product);
                                const isNewSupplier = event.supplier && newVendors.includes(event.supplier);
                                const isNewLocation = event.location && newLocations.includes(event.location);

                                return (
                                <Paper key={index} variant="outlined" sx={{ p: 2, position: 'relative' }}>
                                    <IconButton
                                        onClick={() => removeEvent(index)}
                                        size="small"
                                        sx={{ position: 'absolute', top: 8, right: 8, color: 'text.secondary', '&:hover': { color: 'error.main' } }}
                                    >
                                        <DeleteIcon fontSize="small" />
                                    </IconButton>
                                    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }, gap: 2 }}>
                                        <TextField
                                            label="Event"
                                            value={event.eventType || ''}
                                            onChange={e => handleDataChange(index, 'eventType', e.target.value)}
                                            placeholder="Event Type"
                                            size="small"
                                            fullWidth
                                        />
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Product"
                                                value={event.product || ''}
                                                onChange={e => handleDataChange(index, 'product', e.target.value)}
                                                placeholder="Product"
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewProduct && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <TextField
                                            label="Quantity"
                                            value={event.quantity || ''}
                                            onChange={e => handleDataChange(index, 'quantity', e.target.value)}
                                            placeholder="Quantity"
                                            type="number"
                                            size="small"
                                            fullWidth
                                        />
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Supplier"
                                                value={event.supplier || ''}
                                                onChange={e => handleDataChange(index, 'supplier', e.target.value)}
                                                placeholder="Supplier"
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewSupplier && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Location"
                                                value={event.location || ''}
                                                onChange={e => handleDataChange(index, 'location', e.target.value)}
                                                placeholder="Location"
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewLocation && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <TextField
                                            label="Date"
                                            value={event.date || ''}
                                            onChange={e => handleDataChange(index, 'date', e.target.value)}
                                            type="date"
                                            size="small"
                                            fullWidth
                                            InputLabelProps={{ shrink: true }}
                                        />
                                        <TextField
                                            label="Notes"
                                            value={event.notes || ''}
                                            onChange={e => handleDataChange(index, 'notes', e.target.value)}
                                            placeholder="Notes..."
                                            size="small"
                                            fullWidth
                                            sx={{ gridColumn: { xs: 'span 2', md: 'span 3' } }}
                                        />
                                    </Box>
                                </Paper>
                                );
                            })}
                        </Stack>
                    </Box>
                );
            default:
                return null;
        }
    };
    
    const handleClose = useCallback(() => {
        stopCamera();
        onClose();
    }, [onClose, stopCamera]);
    
    return (
        <Dialog
            open
            onClose={handleClose}
            fullScreen
            PaperProps={{ sx: { bgcolor: 'background.default' } }}
        >
            <DialogTitle
                sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', borderBottom: '1px solid', borderColor: 'divider' }}
            >
                Document Scanner
                <IconButton onClick={handleClose} edge="end" aria-label="Close document scanner">
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent sx={{ p: 0 }}>
                <Box sx={{ flexGrow: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column', minHeight: '70vh' }}>
                    {renderContent()}
                </Box>
                <canvas ref={canvasRef} style={{ display: 'none' }} />
            </DialogContent>
            {step === 'confirm' && (
                <DialogActions sx={{ borderTop: '1px solid', borderColor: 'divider' }}>
                    <Button onClick={() => onSave(extractedEvents)} variant="contained">
                        Save {extractedEvents.length} Items
                    </Button>
                </DialogActions>
            )}
        </Dialog>
    );
};
