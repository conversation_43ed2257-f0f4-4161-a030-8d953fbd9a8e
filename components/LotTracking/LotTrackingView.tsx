import React, { useState, useEffect, useMemo } from 'react';
import { alpha, Box, Button, Stack, Typography, useTheme } from '@mui/material';
import { Lot, LotMovement } from '../../types';
import { onLotsUpdate, onAllMovementsUpdate } from '../../firebase/lotService';
import { LotList } from './LotList';
import { LotDetails } from './LotDetails';
import { AddLotModal } from './AddLotModal';
import { SurfaceCard } from '../common/SurfaceCard';

export const LotTrackingView: React.FC = () => {
  const theme = useTheme();
  const [lots, setLots] = useState<Lot[]>([]);
  const [movements, setMovements] = useState<LotMovement[]>([]);
  const [selectedLot, setSelectedLot] = useState<Lot | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const unsubLots = onLotsUpdate(setLots);
    const unsubMovements = onAllMovementsUpdate(setMovements);
    return () => {
      unsubLots();
      unsubMovements();
    };
  }, []);

  useEffect(() => {
    if (!selectedLot && lots.length > 0) {
      setSelectedLot(lots[0]);
    }
  }, [lots, selectedLot]);

  const filteredLots = useMemo(() => {
    if (!searchQuery) {
      return lots;
    }
    const queryValue = searchQuery.toLowerCase();
    return lots.filter(
      (lot) =>
        lot.lotId.toLowerCase().includes(queryValue) ||
        lot.species.toLowerCase().includes(queryValue),
    );
  }, [lots, searchQuery]);

  return (
    <Stack spacing={4} width="100%">
      <Stack
        direction={{ xs: 'column', md: 'row' }}
        spacing={2}
        justifyContent="space-between"
        alignItems={{ xs: 'flex-start', md: 'center' }}
      >
        <Stack spacing={1}>
          <Typography variant="h4" sx={{ fontWeight: 700 }}>
            Lot Movement Tracking
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Monitor and manage the real-time location and history of seafood lots.
          </Typography>
        </Stack>

        <Button
          variant="contained"
          onClick={() => setIsModalOpen(true)}
          sx={{
            textTransform: 'none',
            minWidth: 160,
            bgcolor: theme.palette.primary.main,
            '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.9) },
          }}
        >
          Track New Lot
        </Button>
      </Stack>

      <Box
        sx={{
          display: 'grid',
          gap: 3,
          gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' },
          alignItems: 'flex-start',
        }}
      >
        <LotList
          lots={filteredLots}
          movements={movements}
          selectedLot={selectedLot}
          onSelectLot={setSelectedLot}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
        />

        {selectedLot ? (
          <LotDetails lot={selectedLot} />
        ) : (
          <SurfaceCard sx={{ textAlign: 'center', p: 3 }}>
            <Typography variant="body2" color="text.secondary">
              Select a lot to see details.
            </Typography>
          </SurfaceCard>
        )}
      </Box>

      {isModalOpen && <AddLotModal onClose={() => setIsModalOpen(false)} />}
    </Stack>
  );
};