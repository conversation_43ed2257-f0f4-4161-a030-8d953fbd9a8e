import React, { useMemo } from 'react';
import {
  Box,
  Chip,
  InputAdornment,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { Lot, LotMovement } from '../../types';
import { SurfaceCard } from '../common/SurfaceCard';
import { getStatusChipProps } from './statusHelpers';

interface LotListProps {
  lots: Lot[];
  movements: LotMovement[];
  selectedLot: Lot | null;
  onSelectLot: (lot: Lot) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export const LotList: React.FC<LotListProps> = ({ lots, movements, selectedLot, onSelectLot, searchQuery, onSearchChange }) => {
  const latestMovements = useMemo(() => {
    const movementMap = new Map<string, LotMovement>();
    movements.forEach(movement => {
        if (!movementMap.has(movement.lotId)) {
            movementMap.set(movement.lotId, movement);
        }
    });
    return movementMap;
  }, [movements]);

  return (
    <Stack spacing={3} sx={{ flex: 1 }}>
      <TextField
        placeholder="Search by Lot ID, Species, Origin..."
        value={searchQuery}
        onChange={(event) => onSearchChange(event.target.value)}
        fullWidth
        size="small"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon fontSize="small" />
            </InputAdornment>
          ),
        }}
      />

      <Stack direction="row" spacing={1} sx={{ overflowX: 'auto', pb: 1 }}>
        {['Status: All', 'Location: All', 'Species: All', 'Date Range'].map((label) => (
          <Chip key={label} label={label} variant="outlined" sx={{ flexShrink: 0 }} />
        ))}
      </Stack>

      <TableContainer component={SurfaceCard} sx={{ p: 0 }}>
        <Table size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: 'background.surface' }}>
              <TableCell>
                <Typography variant="overline" color="text.secondary">
                  Lot ID
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="overline" color="text.secondary">
                  Species
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="overline" color="text.secondary">
                  Current Location
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="overline" color="text.secondary">
                  Status
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="overline" color="text.secondary">
                  Last Updated
                </Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {lots.map((lot) => {
              const latestMovement = latestMovements.get(lot.id);
              const chipProps = getStatusChipProps(lot.status);
              return (
                <TableRow
                  key={lot.id}
                  hover
                  selected={selectedLot?.id === lot.id}
                  onClick={() => onSelectLot(lot)}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell sx={{ width: '15%' }}>
                    <Typography variant="body2" fontWeight={600} color="primary.main">
                      {lot.lotId}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ width: '20%' }}>
                    <Typography variant="body2" color="text.primary">
                      {lot.species}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ width: '25%' }}>
                    <Typography variant="body2" color="text.secondary">
                      {latestMovement ? latestMovement.location : 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ width: '15%' }}>
                    <Chip size="small" {...chipProps} />
                  </TableCell>
                  <TableCell sx={{ width: '25%' }}>
                    <Typography variant="body2" color="text.secondary">
                      {latestMovement ? new Date(latestMovement.timestamp.seconds * 1000).toLocaleString() : 'N/A'}
                    </Typography>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
        {lots.length === 0 && (
          <Box sx={{ py: 4, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              No lots found.
            </Typography>
          </Box>
        )}
      </TableContainer>
    </Stack>
  );
};