import React from 'react';
import { <PERSON>, But<PERSON>, Stack, Typography, useTheme } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { LotMovement } from '../../types';
import { SurfaceCard } from '../common/SurfaceCard';

interface MovementHistoryProps {
  movements: LotMovement[];
  onAddMovement: () => void;
}

const getMovementColor = (type: string, palette: { success: { main: string }; error: { main: string }; info: { main: string }; text: { secondary: string } }) => {
  switch (type) {
    case 'Arrival':
      return palette.success.main;
    case 'Departure':
      return palette.error.main;
    case 'Processing':
      return palette.info.main;
    default:
      return palette.text.secondary;
  }
};

export const MovementHistory: React.FC<MovementHistoryProps> = ({ movements, onAddMovement }) => {
  const theme = useTheme();

  return (
    <SurfaceCard sx={{ flex: 1, display: 'flex', flexDirection: 'column', p: 3 }}>
      <Typography variant="subtitle1" fontWeight={700} color="text.primary" sx={{ mb: 2 }}>
        Movement History
      </Typography>
      <Box sx={{ flex: 1, overflowY: 'auto', pr: 1 }}>
        <Stack
          component="ul"
          spacing={2.5}
          sx={{
            listStyle: 'none',
            pl: 0,
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              left: 12,
              top: 0,
              bottom: 0,
              width: 2,
              bgcolor: 'divider',
            },
          }}
        >
          {movements.map((movement) => (
            <Stack
              component="li"
              key={movement.id}
              spacing={0.5}
              sx={{ position: 'relative', pl: 4 }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  left: 4,
                  top: 4,
                  width: 16,
                  height: 16,
                  borderRadius: '50%',
                  border: (theme) => `3px solid ${theme.palette.background.paper}`,
                  bgcolor: getMovementColor(movement.type, {
                    success: theme.palette.success,
                    error: theme.palette.error,
                    info: theme.palette.info,
                    text: theme.palette.text,
                  }),
                }}
              />
              <Typography variant="body2" fontWeight={600} color="text.primary">
                {movement.description}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {new Date(movement.timestamp.seconds * 1000).toLocaleString()}
              </Typography>
            </Stack>
          ))}
          {movements.length === 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ pl: 4 }}>
              No movements recorded.
            </Typography>
          )}
        </Stack>
      </Box>
      <Button
        onClick={onAddMovement}
        variant="outlined"
        startIcon={<AddIcon />}
        sx={{ mt: 2, alignSelf: 'flex-start', textTransform: 'none' }}
      >
        Add New Movement
      </Button>
    </SurfaceCard>
  );
};