import React, { useState, useEffect } from 'react';
import { Box, Typography, Stack, Button, TextField, useTheme } from '@mui/material';
import { alpha } from '@mui/material/styles';
import { db } from '../firebase/config';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, serverTimestamp, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { Vendor } from '../types';
import { BuildingStorefrontIcon, PlusIcon, PhotoIcon } from './IconComponents';
import { uploadImage } from '../utils/imageUtils';
import { docToPlainObject } from '../utils/firestoreUtils';
import { SurfaceCard } from './common/SurfaceCard';

export const VendorsView: React.FC = () => {
    const [vendors, setVendors] = useState<Vendor[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);
    
    // Form state
    const [editingId, setEditingId] = useState<string | null>(null);
    const [name, setName] = useState('');
    const [contactPerson, setContactPerson] = useState('');
    const [phone, setPhone] = useState('');
    const [email, setEmail] = useState('');
    const [address, setAddress] = useState('');
    const [notes, setNotes] = useState('');
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [productsCarried, setProductsCarried] = useState('');
    const [sourcingLeadTimeDays, setSourcingLeadTimeDays] = useState('');

    useEffect(() => {
        try {
            const q = query(collection(db, "vendors"), orderBy("name"));
            const unsubscribe = onSnapshot(q, (querySnapshot: QuerySnapshot<DocumentData>) => {
                const vendorsList: Vendor[] = querySnapshot.docs.map(doc => docToPlainObject<Vendor>(doc));
                setVendors(vendorsList);
                setLoading(false);
            }, (err) => {
                console.error(err);
                setError("Failed to fetch vendors.");
                setLoading(false);
            });
            return () => unsubscribe();
        } catch(e) {
            console.error(e);
            setError("Failed to initialize Firebase for vendors.");
            setLoading(false);
        }
    }, []);

    const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            const previewUrl = URL.createObjectURL(file);
            setImagePreview(previewUrl);
        }
    };

    const resetForm = () => {
        setEditingId(null);
        setName('');
        setContactPerson('');
        setPhone('');
        setEmail('');
        setAddress('');
        setNotes('');
        setImageFile(null);
        setImagePreview(null);
        setProductsCarried('');
        setSourcingLeadTimeDays('');
        setIsFormOpen(false);
    };

    const handleEdit = (vendor: Vendor) => {
        setEditingId(vendor.id);
        setName(vendor.name);
        setContactPerson(vendor.contactPerson || '');
        setPhone(vendor.phone || '');
        setEmail(vendor.email || '');
        setAddress(vendor.address || '');
        setNotes(vendor.notes || '');
        setImagePreview(vendor.imageUrl || null);
        setProductsCarried(vendor.productsCarried?.join('\n') || '');
        setSourcingLeadTimeDays(vendor.sourcingLeadTimeDays?.toString() || '');
        setImageFile(null);
        setIsFormOpen(true);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!name) {
            alert("Vendor name is required.");
            return;
        }
        
        const productsArray = productsCarried.split('\n').map(s => s.trim()).filter(Boolean);

        const vendorData: Partial<Vendor> = { 
            name, 
            contactPerson, 
            phone, 
            email, 
            address, 
            notes, 
            productsCarried: productsArray, 
            sourcingLeadTimeDays: parseInt(sourcingLeadTimeDays, 10) || undefined 
        };

        if (imageFile) {
            try {
                const imageUrl = await uploadImage(imageFile, `vendors/${Date.now()}_${imageFile.name}`);
                vendorData.imageUrl = imageUrl;
            } catch (error) {
                alert("Failed to upload image.");
                return;
            }
        } else if (editingId && imagePreview) {
            vendorData.imageUrl = imagePreview;
        }

        try {
            if (editingId) {
                const vendorRef = doc(db, "vendors", editingId);
                await updateDoc(vendorRef, vendorData);
            } else {
                await addDoc(collection(db, "vendors"), vendorData);
            }
            resetForm();
        } catch (error) {
            console.error("Error saving vendor: ", error);
            alert("Failed to save vendor.");
        }
    };

    const theme = useTheme();

    return (
        <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%' }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ pb: 2, borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                <Stack direction="row" alignItems="center" spacing={2}>
                    <Box sx={{ bgcolor: alpha(theme.palette.secondary.main, 0.1), color: 'secondary.main', p: 1, borderRadius: 1 }}>
                        <BuildingStorefrontIcon className="h-6 w-6" />
                    </Box>
                    <Typography variant="h5" component="h1" sx={{ color: 'text.primary', fontWeight: 'bold' }}>
                        Vendor Management
                    </Typography>
                </Stack>
                <Button 
                    onClick={() => { resetForm(); setIsFormOpen(true); }} 
                    variant="contained" 
                    startIcon={<PlusIcon className="h-5 w-5" />}
                >
                    Add Vendor
                </Button>
            </Stack>

            {isFormOpen && (
                <SurfaceCard sx={{ bgcolor: 'background.surface', p: 3, mb: 3 }}>
                    <Typography variant="h6" sx={{ color: 'text.primary', mb: 2, fontWeight: 600 }}>
                        {editingId ? 'Edit Vendor' : 'Add New Vendor'}
                    </Typography>
                    <form onSubmit={handleSubmit}>
                        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' }, gap: 2 }}>
                            <TextField label="Vendor Name" id="name" value={name} onChange={e => setName(e.target.value)} required fullWidth />
                            <TextField label="Contact Person" id="contactPerson" value={contactPerson} onChange={e => setContactPerson(e.target.value)} fullWidth />
                            <TextField label="Phone" id="phone" type="tel" value={phone} onChange={e => setPhone(e.target.value)} fullWidth />
                            <TextField label="Email" id="email" type="email" value={email} onChange={e => setEmail(e.target.value)} fullWidth />
                            <TextField label="Address" id="address" value={address} onChange={e => setAddress(e.target.value)} multiline rows={2} fullWidth sx={{ gridColumn: { md: 'span 2' } }} />
                            <TextField label="Products Carried (one per line)" id="productsCarried" value={productsCarried} onChange={e => setProductsCarried(e.target.value)} multiline rows={3} fullWidth />
                            <TextField label="Sourcing Lead Time (Days)" id="sourcingLeadTimeDays" type="number" value={sourcingLeadTimeDays} onChange={e => setSourcingLeadTimeDays(e.target.value)} fullWidth />
                            <TextField label="Notes" id="notes" value={notes} onChange={e => setNotes(e.target.value)} multiline rows={3} fullWidth sx={{ gridColumn: { md: 'span 2' } }} />
                            <Box sx={{ gridColumn: { md: 'span 2' } }}>
                                <Typography variant="body2" sx={{ color: 'text.primary', fontWeight: 500, mb: 1 }}>Vendor Image</Typography>
                                <Stack direction="row" spacing={2} alignItems="center">
                                    <Box sx={{ flexShrink: 0 }}>
                                        {imagePreview ? (
                                            <img style={{ height: 64, width: 64, objectFit: 'cover', borderRadius: 4 }} src={imagePreview} alt="Vendor" />
                                        ) : (
                                            <Box sx={{ height: 64, width: 64, bgcolor: 'action.hover', borderRadius: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                                <PhotoIcon className="h-8 w-8" />
                                            </Box>
                                        )}
                                    </Box>
                                    <Button variant="outlined" component="label" fullWidth>
                                        Choose File
                                        <input type="file" onChange={handleImageSelect} accept="image/*" hidden />
                                    </Button>
                                </Stack>
                            </Box>
                        </Box>
                        <Stack direction="row" justifyContent="flex-end" spacing={2} sx={{ mt: 2 }}>
                            <Button type="button" onClick={resetForm} variant="outlined">Cancel</Button>
                            <Button type="submit" variant="contained">{editingId ? 'Update Vendor' : 'Save Vendor'}</Button>
                        </Stack>
                    </form>
                </SurfaceCard>
            )}

            {loading && <Typography sx={{ color: 'text.secondary' }}>Loading vendors...</Typography>}
            {error && <Typography sx={{ color: 'error.main' }}>{error}</Typography>}
            {!loading && !error && (
                <Box sx={{ 
                    display: 'grid', 
                    gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' },
                    gap: 2 
                }}>
                    {vendors.length === 0 ? (
                        <Typography sx={{ textAlign: 'center', color: 'text.secondary', py: 2, gridColumn: { md: 'span 2' } }}>
                            No vendors added yet.
                        </Typography>
                    ) : (
                        vendors.map(vendor => (
                            <SurfaceCard key={vendor.id} hover sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                                <Stack direction="row" spacing={2} alignItems="flex-start">
                                    {vendor.imageUrl && (
                                        <img src={vendor.imageUrl} alt={vendor.name} style={{ height: 64, width: 64, borderRadius: 8, objectFit: 'cover' }} />
                                    )}
                                    <Box sx={{ flexGrow: 1 }}>
                                        <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 600 }}>
                                            {vendor.name}
                                        </Typography>
                                        {vendor.contactPerson && (
                                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                                {vendor.contactPerson} - {vendor.phone}
                                            </Typography>
                                        )}
                                        {vendor.sourcingLeadTimeDays && (
                                            <Typography variant="caption" sx={{ color: 'text.secondary', mt: 0.5, display: 'block' }}>
                                                Lead Time: <Box component="span" sx={{ fontWeight: 'bold' }}>{vendor.sourcingLeadTimeDays} days</Box>
                                            </Typography>
                                        )}
                                    </Box>
                                </Stack>
                                <Box sx={{ mt: 1 }}>
                                    <Typography variant="subtitle2" sx={{ color: 'text.primary', fontWeight: 600 }}>Products:</Typography>
                                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                        {vendor.productsCarried?.join(', ') || 'N/A'}
                                    </Typography>
                                </Box>
                                <Box sx={{ mt: 1, pt: 1, borderTop: 1, borderColor: 'divider', display: 'flex', justifyContent: 'flex-end' }}>
                                    <Button onClick={() => handleEdit(vendor)} size="small" sx={{ color: 'primary.main', fontWeight: 600 }}>
                                        Edit
                                    </Button>
                                </Box>
                            </SurfaceCard>
                        ))
                    )}
                </Box>
            )}
        </SurfaceCard>
    );
};