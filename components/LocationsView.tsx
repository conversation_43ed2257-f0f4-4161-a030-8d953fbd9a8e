

import React, { useState, useEffect, useMemo } from 'react';
import { Box, Typography, Stack, Button, TextField, IconButton, useTheme, alpha } from '@mui/material';
import { db } from '../firebase/config';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { Location, HACCPEvent } from '../types';
import { MapPinIcon, PlusIcon, CubeIcon, EyeIcon } from './IconComponents';
import { FreezerLayoutModal } from './FreezerLayoutModal';
import { docToPlainObject } from '../utils/firestoreUtils';
import { SurfaceCard } from './common/SurfaceCard';

interface LocationsViewProps {
    events: HACCPEvent[];
}

export const LocationsView: React.FC<LocationsViewProps> = ({ events }) => {
    const [locations, setLocations] = useState<Location[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [isLayoutModalOpen, setIsLayoutModalOpen] = useState(false);
    const [selectedLocationForLayout, setSelectedLocationForLayout] = useState<Location | null>(null);
    
    // Form state
    const [editingId, setEditingId] = useState<string | null>(null);
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');

    const inventoryByLocation = useMemo(() => {
        const inventoryMap: { [locationName: string]: { [product: string]: number } } = {};

        [...events].sort((a, b) => {
            const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
            const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            return timeA - timeB;
        }).forEach(event => {
            if (!event.product) return;
            const prod = event.product;
            
            switch (event.eventType) {
                case 'receiving':
                    if(event.location) {
                        if (!inventoryMap[event.location]) inventoryMap[event.location] = {};
                        if (!inventoryMap[event.location][prod]) inventoryMap[event.location][prod] = 0;
                        inventoryMap[event.location][prod] += event.quantity || 0;
                    }
                    break;
                case 'sales':
                case 'disposal':
                     // This simple logic assumes a location is specified for the outgoing product.
                     // A more robust system would trace by batch number.
                    if (event.location && inventoryMap[event.location]?.[prod]) {
                        inventoryMap[event.location][prod] -= event.quantity || 0;
                    }
                    break;
                case 'relocation':
                    const qty = event.quantity || 0;
                    if (event.fromLocation && inventoryMap[event.fromLocation]?.[prod]) {
                        inventoryMap[event.fromLocation][prod] -= qty;
                    }
                    if (event.location) { // 'location' is the 'to' location
                        if (!inventoryMap[event.location]) inventoryMap[event.location] = {};
                        if (!inventoryMap[event.location][prod]) inventoryMap[event.location][prod] = 0;
                        inventoryMap[event.location][prod] += qty;
                    }
                    break;
            }
        });
        
        // Clean up zero or negative quantities
        Object.keys(inventoryMap).forEach(loc => {
            Object.keys(inventoryMap[loc]).forEach(prod => {
                if (inventoryMap[loc][prod] <= 0.01) {
                    delete inventoryMap[loc][prod];
                }
            });
        });

        return inventoryMap;

    }, [events]);

    useEffect(() => {
        try {
            const q = query(collection(db, "locations"), orderBy("name"));
            const unsubscribe = onSnapshot(q, (querySnapshot: QuerySnapshot<DocumentData>) => {
                const locationsList: Location[] = querySnapshot.docs.map(doc => docToPlainObject<Location>(doc));
                setLocations(locationsList);
                setLoading(false);
            }, (err) => {
                console.error(err);
                setError("Failed to fetch locations.");
                setLoading(false);
            });
            return () => unsubscribe();
        } catch(e) {
            console.error(e);
            setError("Failed to initialize Firebase for locations.");
            setLoading(false);
        }
    }, []);

    const resetForm = () => {
        setEditingId(null);
        setName('');
        setDescription('');
        setIsFormOpen(false);
    };

    const handleEdit = (location: Location) => {
        setEditingId(location.id);
        setName(location.name);
        setDescription(location.description || '');
        setIsFormOpen(true);
    };

    const handleOpenLayout = (location: Location) => {
        setSelectedLocationForLayout(location);
        setIsLayoutModalOpen(true);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!name) {
            alert("Location name is required.");
            return;
        }

        const locationData = { name, description };

        try {
            if (editingId) {
                const locationRef = doc(db, "locations", editingId);
                await updateDoc(locationRef, locationData);
            } else {
                await addDoc(collection(db, "locations"), locationData);
            }
            resetForm();
        } catch (error) {
            console.error("Error saving location: ", error);
            alert("Failed to save location.");
        }
    };

    const theme = useTheme();

    return (
        <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%' }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ pb: 2, borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                <Stack direction="row" alignItems="center" spacing={2}>
                    <Box sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: 'info.main', p: 1, borderRadius: 1 }}>
                        <MapPinIcon className="h-6 w-6" />
                    </Box>
                    <Typography variant="h5" component="h1" sx={{ color: 'text.primary', fontWeight: 'bold' }}>
                        Location Management & Inventory
                    </Typography>
                </Stack>
                <Button 
                    onClick={() => { resetForm(); setIsFormOpen(true); }} 
                    variant="contained" 
                    startIcon={<PlusIcon className="h-5 w-5" />}
                >
                    Add Location
                </Button>
            </Stack>

            {isFormOpen && (
                <SurfaceCard sx={{ bgcolor: 'background.surface', p: 3, mb: 3 }}>
                    <Typography variant="h6" sx={{ color: 'text.primary', mb: 2, fontWeight: 600 }}>
                        {editingId ? 'Edit Location' : 'Add New Location'}
                    </Typography>
                    <form onSubmit={handleSubmit}>
                        <Stack spacing={2}>
                            <TextField
                                label="Location Name"
                                id="name"
                                value={name}
                                onChange={e => setName(e.target.value)}
                                required
                                fullWidth
                            />
                            <TextField
                                label="Description"
                                id="description"
                                value={description}
                                onChange={e => setDescription(e.target.value)}
                                multiline
                                rows={3}
                                fullWidth
                            />
                            <Stack direction="row" justifyContent="flex-end" spacing={2}>
                                <Button type="button" onClick={resetForm} variant="outlined">
                                    Cancel
                                </Button>
                                <Button type="submit" variant="contained">
                                    {editingId ? 'Update Location' : 'Save Location'}
                                </Button>
                            </Stack>
                        </Stack>
                    </form>
                </SurfaceCard>
            )}

            {loading && <Typography sx={{ color: 'text.secondary' }}>Loading locations...</Typography>}
            {error && <Typography sx={{ color: 'error.main' }}>{error}</Typography>}
            {!loading && !error && (
                <Box sx={{ 
                    display: 'grid', 
                    gridTemplateColumns: { xs: '1fr', lg: 'repeat(2, 1fr)' },
                    gap: 3 
                }}>
                    {locations.length === 0 ? (
                        <Typography sx={{ textAlign: 'center', color: 'text.secondary', py: 2, gridColumn: { lg: 'span 2' } }}>
                            No locations added yet.
                        </Typography>
                    ) : (
                        locations.map(location => {
                            const inventory = inventoryByLocation[location.name] || {};
                            const productCount = Object.keys(inventory).length;
                            return (
                                <SurfaceCard key={location.id} hover sx={{ display: 'flex', flexDirection: 'column' }}>
                                    <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                                        <Box>
                                            <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 600 }}>
                                                {location.name}
                                            </Typography>
                                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                                {location.description}
                                            </Typography>
                                        </Box>
                                        <Stack direction="row" spacing={1} alignItems="center">
                                            <IconButton 
                                                onClick={() => handleOpenLayout(location)} 
                                                size="small"
                                                title="View Layout"
                                                sx={{ color: 'text.secondary', '&:hover': { color: 'primary.main' } }}
                                            >
                                                <EyeIcon className="h-5 w-5"/>
                                            </IconButton>
                                            <Button 
                                                onClick={() => handleEdit(location)} 
                                                size="small"
                                                sx={{ color: 'primary.main', fontWeight: 600 }}
                                            >
                                                Edit
                                            </Button>
                                        </Stack>
                                    </Stack>
                                    <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider', flexGrow: 1 }}>
                                        <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                                            <CubeIcon className="w-4 h-4"/>
                                            <Typography variant="subtitle2" sx={{ color: 'text.primary', fontWeight: 600 }}>
                                                Current Inventory
                                            </Typography>
                                        </Stack>
                                        {productCount > 0 ? (
                                            <Box sx={{ 
                                                display: 'grid', 
                                                gridTemplateColumns: 'repeat(2, 1fr)',
                                                columnGap: 2,
                                                rowGap: 0.5
                                            }}>
                                                {Object.entries(inventory).map(([product, quantity]) => (
                                                    <Stack key={product} direction="row" justifyContent="space-between" sx={{ fontSize: '0.875rem' }}>
                                                        <Typography 
                                                            variant="body2" 
                                                            sx={{ color: 'text.secondary', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                                                            title={product}
                                                        >
                                                            {product}
                                                        </Typography>
                                                        <Typography variant="body2" sx={{ color: 'text.primary', fontWeight: 'bold', flexShrink: 0, ml: 1 }}>
                                                            {(quantity as number).toFixed(2)} lbs
                                                        </Typography>
                                                    </Stack>
                                                ))}
                                            </Box>
                                        ) : (
                                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                                No inventory tracked at this location.
                                            </Typography>
                                        )}
                                    </Box>
                                </SurfaceCard>
                            )
                        })
                    )}
                </Box>
            )}
            {isLayoutModalOpen && selectedLocationForLayout && (
                <FreezerLayoutModal
                    isOpen={isLayoutModalOpen}
                    onClose={() => setIsLayoutModalOpen(false)}
                    location={selectedLocationForLayout}
                    inventory={inventoryByLocation[selectedLocationForLayout.name] || {}}
                />
            )}
        </SurfaceCard>
    );
};