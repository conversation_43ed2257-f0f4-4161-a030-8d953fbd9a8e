
import React from 'react';
import { Paper, Box, Stack, Typography, alpha, useTheme } from '@mui/material';
import { ConversationTurn } from '../types';

interface ConversationLogProps {
  turns: ConversationTurn[];
}

export const ConversationLog: React.FC<ConversationLogProps> = ({ turns }) => {
  const theme = useTheme();
  
  return (
    <Paper 
      variant="outlined"
      sx={{ 
        p: 3, 
        height: 256, 
        overflowY: 'auto', 
        bgcolor: (t) => alpha(t.palette.background.paper, 0.5),
        borderColor: 'divider',
        display: 'flex',
        flexDirection: 'column-reverse'
      }}
    >
      <Stack spacing={2}>
        {turns.map((turn, index) => (
          <Box 
            key={index} 
            sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: turn.speaker === 'user' ? 'flex-start' : 'flex-end'
            }}
          >
            <Box
              sx={{
                borderRadius: 2,
                px: 2,
                py: 1,
                maxWidth: '75%',
                bgcolor: turn.speaker === 'user' ? 'primary.main' : 'background.surface',
                color: turn.speaker === 'user' ? 'primary.contrastText' : 'text.primary',
              }}
            >
              <Typography variant="body2">{turn.text}</Typography>
            </Box>
            <Typography 
              variant="caption" 
              sx={{ 
                color: 'text.secondary', 
                mt: 0.5,
                textTransform: 'capitalize'
              }}
            >
              {turn.speaker}
            </Typography>
          </Box>
        ))}
        {turns.length === 0 && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Conversation will appear here.
            </Typography>
          </Box>
        )}
      </Stack>
    </Paper>
  );
};
