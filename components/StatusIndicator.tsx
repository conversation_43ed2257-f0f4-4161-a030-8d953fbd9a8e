
import React from 'react';
import { Box, Typography, useTheme, alpha } from '@mui/material';

interface StatusIndicatorProps {
  status: string;
  isRecording: boolean;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status, isRecording }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 1.5,
        p: 2,
        bgcolor: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(8px)',
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        boxShadow: theme.shadows[4],
      }}
    >
      {isRecording && (
        <Box
          sx={{
            position: 'relative',
            display: 'flex',
            width: 12,
            height: 12,
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              borderRadius: '50%',
              bgcolor: theme.palette.error.light,
              opacity: 0.75,
              animation: 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
              '@keyframes ping': {
                '75%, 100%': {
                  transform: 'scale(2)',
                  opacity: 0,
                },
              },
            }}
          />
          <Box
            sx={{
              position: 'relative',
              width: 12,
              height: 12,
              borderRadius: '50%',
              bgcolor: theme.palette.error.main,
            }}
          />
        </Box>
      )}
      <Typography variant="body1" fontWeight="500" color="text.primary">
        {status}
      </Typography>
    </Box>
  );
};
