import React, { useMemo, useState } from 'react';
import { addDoc, collection } from 'firebase/firestore';
import { HACCPEvent, Species, Vendor } from '../types';
import { db } from '../firebase/config';
import { AddProductModal } from './AddProductModal';
import { Box, Typography, Stack, Button, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Checkbox, Pagination, IconButton, Avatar } from '@mui/material';
import { SurfaceCard } from './common/SurfaceCard';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ArchiveIcon from '@mui/icons-material/Archive';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface InventoryManagementProps {
  events: HACCPEvent[];
  species: Species[];
  vendors: Vendor[];
}

type InventoryItem = {
  id: string;
  name: string;
  category?: 'Fish' | 'Shellfish' | 'Other';
  stock: number;
  unitPrice?: number;
  status: 'In Stock' | 'Low Stock' | 'Out of Stock';
  supplier?: string;
  dateAdded?: string;
  imageUrl?: string;
};

const InventoryManagement: React.FC<InventoryManagementProps> = ({ events, species, vendors }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const itemsPerPage = 10;

  const inventoryData = useMemo<InventoryItem[]>(() => {
    const inventoryMap: { [productName: string]: InventoryItem } = {};

    // Initialize with all species to ensure products with 0 stock are shown
    species.forEach(s => {
      inventoryMap[s.name] = {
        id: s.id,
        name: s.name,
        category: s.category,
        stock: 0,
        status: 'Out of Stock',
        imageUrl: s.imageUrl,
      };
    });

    // Process events to calculate stock and other details
    [...events]
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .forEach(event => {
        if (!event.product || !inventoryMap[event.product]) return;

        const item = inventoryMap[event.product];
        const quantity = event.quantity || 0;

        switch (event.eventType) {
          case 'receiving':
            item.stock += quantity;
            item.supplier = event.supplier;
            item.unitPrice = event.unitPrice;
            item.dateAdded = event.date;
            break;
          case 'sales':
          case 'disposal':
            item.stock -= quantity;
            break;
        }
      });

    // Set status based on final stock count
    Object.values(inventoryMap).forEach(item => {
      if (item.stock <= 0) {
        item.status = 'Out of Stock';
        item.stock = 0; // Ensure stock is not negative
      } else if (item.stock < 50) {
        item.status = 'Low Stock';
      } else {
        item.status = 'In Stock';
      }
    });

    return Object.values(inventoryMap);
  }, [events, species]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return inventoryData.slice(startIndex, startIndex + itemsPerPage);
  }, [inventoryData, currentPage]);

  const totalPages = Math.ceil(inventoryData.length / itemsPerPage);

  const handleExport = () => {
    const headers = ["Product Name", "Category", "Stock", "Unit Price", "Status", "Supplier", "Date Added"];
    const csvContent = [
      headers.join(','),
      ...inventoryData.map(item => [
        item.name,
        item.category || 'N/A',
        item.stock.toFixed(2),
        item.unitPrice?.toFixed(2) || 'N/A',
        item.status,
        item.supplier || 'N/A',
        item.dateAdded || 'N/A'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.href) {
      URL.revokeObjectURL(link.href);
    }
    link.href = URL.createObjectURL(blob);
    link.download = 'inventory_data.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleAddProduct = async (newSpecies: Partial<Species>) => {
    if (!newSpecies.name) {
      alert('Product name is required.');
      return;
    }

    const speciesData: Partial<Species> = {
      name: newSpecies.name,
      category: newSpecies.category || 'Other',
      productForms: newSpecies.productForms || [],
      qualityControlNotes: 'N/A',
    };

    try {
      await addDoc(collection(db, 'species'), speciesData);
      setIsAddModalOpen(false);
    } catch (error) {
      console.error('Error adding new product:', error);
      alert('Failed to add new product. Please try again.');
    }
  };

  const getStatusChip = (status: InventoryItem['status']) => {
    switch (status) {
      case 'In Stock':
        return <Chip label="In Stock" color="success" size="small" />;
      case 'Low Stock':
        return <Chip label="Low Stock" color="warning" size="small" />;
      case 'Out of Stock':
        return <Chip label="Out of Stock" color="error" size="small" />;
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', flex: 1 }}>
      <SurfaceCard sx={{ mb: 2 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap" spacing={2}>
          <Typography variant="h4" sx={{ color: 'text.primary', fontWeight: 900 }}>
            Inventory Management
          </Typography>
          <Stack direction="row" spacing={1.5}>
            <Button
              onClick={handleExport}
              variant="outlined"
              startIcon={<FileDownloadIcon />}
              sx={{ fontWeight: 600 }}
            >
              Export Data
            </Button>
            <Button
              onClick={() => setIsAddModalOpen(true)}
              variant="contained"
              startIcon={<AddIcon />}
              sx={{ fontWeight: 600 }}
            >
              Add New Product
            </Button>
          </Stack>
        </Stack>
      </SurfaceCard>
      <SurfaceCard variant="outlined" sx={{ mb: 2 }}>
        <Stack direction="row" spacing={1.5} flexWrap="wrap">
          <Button
            variant="outlined"
            endIcon={<ExpandMoreIcon />}
            size="small"
          >
            Product Type
          </Button>
          <Button
            variant="outlined"
            endIcon={<ExpandMoreIcon />}
            size="small"
          >
            Status
          </Button>
          <Button
            variant="outlined"
            endIcon={<ExpandMoreIcon />}
            size="small"
          >
            Supplier
          </Button>
          <Button
            variant="outlined"
            endIcon={<ExpandMoreIcon />}
            size="small"
          >
            Origin
          </Button>
        </Stack>
      </SurfaceCard>
      <SurfaceCard variant="outlined">
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox" sx={{ bgcolor: 'background.surface', borderColor: 'divider' }}>
                  <Checkbox />
                </TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', width: 64 }}></TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', fontWeight: 600 }}>Product Name</TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', fontWeight: 600 }}>Category</TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', fontWeight: 600 }}>Stock</TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', fontWeight: 600 }}>Unit Price</TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', fontWeight: 600 }}>Status</TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', fontWeight: 600 }}>Supplier</TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', fontWeight: 600 }}>Date Added</TableCell>
                <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', fontWeight: 600, textAlign: 'center' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedData.map(item => (
                <TableRow 
                  key={item.id}
                  sx={{ 
                    '&:hover': { bgcolor: 'action.hover' },
                    '& td': { borderColor: 'divider' }
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox />
                  </TableCell>
                  <TableCell>
                    <Avatar
                      src={item.imageUrl || 'https://via.placeholder.com/150'}
                      variant="rounded"
                      sx={{ width: 40, height: 40 }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {item.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {item.category || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {item.stock.toFixed(2)} kg
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      ${item.unitPrice?.toFixed(2) || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>{getStatusChip(item.status)}</TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {item.supplier || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {item.dateAdded || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <IconButton size="small" sx={{ color: 'text.secondary', '&:hover': { color: 'primary.main' } }}>
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton size="small" sx={{ color: 'text.secondary', '&:hover': { color: 'primary.main' } }}>
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                    <IconButton size="small" sx={{ color: 'text.secondary', '&:hover': { color: 'error.main' } }}>
                      <ArchiveIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </SurfaceCard>
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, mt: 2 }}>
        <Pagination
          count={totalPages}
          page={currentPage}
          onChange={(_, page) => setCurrentPage(page)}
          color="primary"
        />
      </Box>
      {isAddModalOpen && (
        <AddProductModal
          onClose={() => setIsAddModalOpen(false)}
          onSave={handleAddProduct}
        />
      )}
    </Box>
  );
};

export default InventoryManagement;
