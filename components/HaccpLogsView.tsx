import React from 'react';
import { Box, Typography } from '@mui/material';
import { SurfaceCard } from './common/SurfaceCard';

export const HaccpLogsView: React.FC = () => {
  return (
    <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%', mx: 'auto' }}>
      <Typography variant="h4" component="h1" sx={{ color: 'text.primary', mb: 3, fontWeight: 'bold' }}>
        HACCP Logs
      </Typography>
      <Typography variant="body1" sx={{ color: 'text.secondary' }}>
        This is a placeholder for the HACCP Logs view.
      </Typography>
    </SurfaceCard>
  );
};