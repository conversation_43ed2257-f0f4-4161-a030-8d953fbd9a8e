import React from 'react';
import { Alert, AlertTitle } from '@mui/material';

export const MockDataWarning: React.FC = () => {
    return (
        <Alert severity="warning" sx={{ mb: 2 }}>
            <AlertTitle>Displaying Sample Data</AlertTitle>
            Live data from the TempStick API cannot be fetched directly from the browser due to CORS security restrictions.
            This component is currently showing pre-loaded sample data.
            <br />
            <strong>Developer Note:</strong> To connect to the live API, a backend proxy is required to handle API requests and add the necessary CORS headers, as described in the documentation.
        </Alert>
    );
};
