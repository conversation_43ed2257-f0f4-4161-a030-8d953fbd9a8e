import React, { useState, useEffect, useCallback } from 'react';
import { alpha, <PERSON><PERSON>, Box, Button, Stack, Typography, useTheme } from '@mui/material';
import { TempStickSensor } from '../types';
import { ThermometerIcon } from './IconComponents';
import { TemperatureChart } from './TemperatureChart';
import { fetchSensors } from '../services/tempstickService';
import { MockDataWarning } from './MockDataWarning';
import { SurfaceCard } from './common/SurfaceCard';

const timeSince = (date: Date): string => {
  const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
  let interval = seconds / 31536000;
  if (interval > 1) return `${Math.floor(interval)} years ago`;
  interval = seconds / 2592000;
  if (interval > 1) return `${Math.floor(interval)} months ago`;
  interval = seconds / 86400;
  if (interval > 1) return `${Math.floor(interval)} days ago`;
  interval = seconds / 3600;
  if (interval > 1) return `${Math.floor(interval)} hours ago`;
  interval = seconds / 60;
  if (interval > 1) return `${Math.floor(interval)} minutes ago`;
  return `${Math.floor(seconds)} seconds ago`;
};

export const TemperatureView: React.FC = () => {
  const theme = useTheme();
  const [sensors, setSensors] = useState<TempStickSensor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [viewingSensor, setViewingSensor] = useState<TempStickSensor | null>(null);

  const getTempAccent = useCallback(
    (temp: number) => {
      if (temp > 140) {
        return {
          text: theme.palette.info.main,
          soft: alpha(theme.palette.info.main, 0.15),
          border: theme.palette.info.main,
        };
      }
      if (temp > 40) {
        return {
          text: theme.palette.error.main,
          soft: alpha(theme.palette.error.main, 0.18),
          border: theme.palette.error.main,
        };
      }
      if (temp > 32) {
        return {
          text: theme.palette.warning.main,
          soft: alpha(theme.palette.warning.main, 0.18),
          border: theme.palette.warning.main,
        };
      }
      return {
        text: theme.palette.success.main,
        soft: alpha(theme.palette.success.main, 0.18),
        border: theme.palette.success.main,
      };
    },
    [theme],
  );

  const loadSensors = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchSensors();
      setSensors(data);
      setLastUpdated(new Date());
    } catch (err) {
      setError(
        'Failed to load sensor data. This is a mock service; if you see this, there is an issue in the mock implementation.',
      );
      setSensors([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadSensors();
    const intervalId = window.setInterval(loadSensors, 300000);
    return () => window.clearInterval(intervalId);
  }, [loadSensors]);

  return (
    <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%' }}>
      <Stack spacing={3}>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          justifyContent="space-between"
          alignItems={{ xs: 'flex-start', sm: 'center' }}
          spacing={2}
          sx={{ pb: 2, borderBottom: 1, borderColor: 'divider' }}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: alpha(theme.palette.error.main, 0.1),
                color: 'error.main',
                borderRadius: 2,
                p: 1.4,
              }}
            >
              <Box component={ThermometerIcon} sx={{ width: 26, height: 26 }} />
            </Box>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>
              Temperature Monitoring
            </Typography>
          </Stack>

          <Button onClick={loadSensors} variant="contained" disabled={loading} sx={{ textTransform: 'none', minWidth: 140 }}>
            {loading ? 'Refreshing…' : 'Refresh'}
          </Button>
        </Stack>

        <MockDataWarning />

        {error && (
          <Alert severity="error" sx={{ borderRadius: 2 }}>
            {error}
          </Alert>
        )}

        {loading && !error ? (
          <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 6 }}>
            Loading sensor data…
          </Typography>
        ) : (
          <>
            <Box
              sx={{
                display: 'grid',
                gap: 2.5,
                gridTemplateColumns: {
                  xs: '1fr',
                  sm: 'repeat(2, 1fr)',
                  lg: 'repeat(3, 1fr)',
                },
              }}
            >
              {sensors.map((sensor) => {
                const accent = getTempAccent(sensor.last_temp);
                return (
                  <Box key={sensor.sensor_id}>
                    <SurfaceCard
                      hover
                      onClick={() => setViewingSensor(sensor)}
                      sx={{
                        p: 3,
                        height: '100%',
                        borderLeft: 4,
                        borderLeftColor: accent.border,
                        transition: theme.transitions.create(['transform', 'box-shadow']),
                      }}
                    >
                      <Stack spacing={2.5} justifyContent="space-between" height="100%">
                        <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                          <Typography variant="subtitle1" fontWeight={600} color="text.primary">
                            {sensor.sensor_name}
                          </Typography>
                          <Stack spacing={0.5} alignItems="flex-end">
                            <Typography variant="h3" component="p" sx={{ fontWeight: 700, color: accent.text }}>
                              {sensor.last_temp.toFixed(1)}°{sensor.temp_f_c?.toUpperCase() || 'F'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {sensor.last_humidity.toFixed(1)}% RH
                            </Typography>
                          </Stack>
                        </Stack>

                        <Box sx={{
                          bgcolor: accent.soft,
                          borderRadius: 2,
                          p: 2,
                        }}>
                          <Stack spacing={0.75}>
                            <Typography variant="body2" color="text.secondary">
                              <Typography component="span" fontWeight={600} color="text.primary">
                                Battery:
                              </Typography>{' '}
                              {sensor.battery_pct}%
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              <Typography component="span" fontWeight={600} color="text.primary">
                                Signal:
                              </Typography>{' '}
                              {sensor.rssi < 0 ? `${sensor.rssi} dBm` : `${sensor.rssi}%`}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              <Typography component="span" fontWeight={600} color="text.primary">
                                Last Update:
                              </Typography>{' '}
                              {timeSince(new Date(sensor.last_checkin))}
                            </Typography>
                          </Stack>
                        </Box>
                      </Stack>
                    </SurfaceCard>
                  </Box>
                );
              })}
            </Box>

            {sensors.length === 0 && !error && !loading && (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 6 }}>
                No sample sensors available.
              </Typography>
            )}
          </>
        )}

        <Typography variant="caption" color="text.secondary" align="center">
          Last refreshed: {lastUpdated ? lastUpdated.toLocaleTimeString() : 'N/A'}
        </Typography>
      </Stack>

      {viewingSensor && (
        <TemperatureChart sensor={viewingSensor} onClose={() => setViewingSensor(null)} />
      )}
    </SurfaceCard>
  );
};
