import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { SurfaceCard } from './common/SurfaceCard';

type OldViews = 'vendors' | 'species' | 'locations' | 'orders' | 'temperature';

interface SettingsViewProps {
    onNavigate: (view: OldViews) => void;
}

export const SettingsView: React.FC<SettingsViewProps> = ({ onNavigate }) => {
  return (
    <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%', mx: 'auto' }}>
      <Typography variant="h4" component="h1" sx={{ color: 'text.primary', mb: 3, fontWeight: 'bold' }}>
        Settings
      </Typography>
      <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4 }}>
        This is a placeholder for the Settings view. You can access the other management pages from here.
      </Typography>

      <Box sx={{ 
        display: 'grid', 
        gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' },
        gap: 2 
      }}>
        <Button
          onClick={() => onNavigate('vendors')}
          variant="outlined"
          sx={{ p: 2, fontWeight: 600 }}
        >
          Manage Vendors
        </Button>
        <Button
          onClick={() => onNavigate('species')}
          variant="outlined"
          sx={{ p: 2, fontWeight: 600 }}
        >
          Manage Species
        </Button>
        <Button
          onClick={() => onNavigate('locations')}
          variant="outlined"
          sx={{ p: 2, fontWeight: 600 }}
        >
          Manage Locations
        </Button>
        <Button
          onClick={() => onNavigate('orders')}
          variant="outlined"
          sx={{ p: 2, fontWeight: 600 }}
        >
          Manage Orders
        </Button>
        <Button
          onClick={() => onNavigate('temperature')}
          variant="outlined"
          sx={{ p: 2, fontWeight: 600 }}
        >
          Temperature Logs
        </Button>
      </Box>
    </SurfaceCard>
  );
};