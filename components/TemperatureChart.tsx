import React, { useState, useEffect, useCallback, useMemo } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import { TempStickSensor, TempStickReading } from '../types';
import { fetchReadings } from '../services/tempstickService'; // Import the new service

interface TemperatureChartProps {
  sensor: TempStickSensor;
  onClose: () => void;
}

type TimeRange = 'today' | '24_hours' | 'last_week' | 'last_month';

const timeRangeOptions: { value: TimeRange; label: string }[] = [
    { value: 'today', label: 'Today' },
    { value: '24_hours', label: '24 Hours' },
    { value: 'last_week', label: 'Last Week' },
    { value: 'last_month', label: 'Last Month' },
];

export const TemperatureChart: React.FC<TemperatureChartProps> = ({ sensor, onClose }) => {
    const theme = useTheme();
    const [readings, setReadings] = useState<TempStickReading[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [timeRange, setTimeRange] = useState<TimeRange>('last_week');

    const loadReadings = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            // Use the new service to fetch readings
            const data = await fetchReadings(sensor.sensor_id, timeRange);
            setReadings(data);
        } catch (err: any) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [sensor.sensor_id, timeRange]);

    useEffect(() => {
        loadReadings();
    }, [loadReadings]);

    const chartData = useMemo(() => {
        if (readings.length === 0) return null;
    
        const parsedReadings = readings.map(r => ({
            time: new Date(r.sensor_time),
            temp: r.temperature,
        })).sort((a,b) => a.time.getTime() - b.time.getTime());
    
        const temps = parsedReadings.map(r => r.temp);
        const minTemp = Math.min(...temps);
        const maxTemp = Math.max(...temps);
        const tempRange = maxTemp - minTemp;
    
        const times = parsedReadings.map(r => r.time.getTime());
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);
        const timeRange = maxTime - minTime;
    
        const yPadding = tempRange * 0.1;
        const chartMinTemp = Math.floor(minTemp - yPadding);
        const chartMaxTemp = Math.ceil(maxTemp + yPadding);
    
        const points = parsedReadings.map(r => {
            const x = timeRange === 0 ? 50 : ((r.time.getTime() - minTime) / timeRange) * 100;
            const y = 100 - ((r.temp - chartMinTemp) / (chartMaxTemp - chartMinTemp)) * 100;
            return { x, y, temp: r.temp, time: r.time };
        });
    
        const path = points.map((p, i) => (i === 0 ? 'M' : 'L') + `${p.x} ${p.y}`).join(' ');
    
        return {
            points,
            path,
            minTemp: chartMinTemp,
            maxTemp: chartMaxTemp,
            startTime: new Date(minTime),
            endTime: new Date(maxTime),
        };
    }, [readings]);
    
    return (
        <Dialog open onClose={onClose} maxWidth="lg" fullWidth>
            <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: 2 }}>
                <Stack spacing={0.25}>
                    <Typography variant="h6">Temperature History</Typography>
                    <Typography variant="body2" color="text.secondary">
                        {sensor.sensor_name}
                    </Typography>
                </Stack>
                <Stack direction="row" spacing={1} alignItems="center">
                    {timeRangeOptions.map((opt) => (
                        <Button
                            key={opt.value}
                            size="small"
                            variant={timeRange === opt.value ? 'contained' : 'outlined'}
                            onClick={() => setTimeRange(opt.value)}
                        >
                            {opt.label}
                        </Button>
                    ))}
                    <IconButton onClick={onClose} aria-label="Close temperature history dialog">
                        <CloseIcon />
                    </IconButton>
                </Stack>
            </DialogTitle>
            <DialogContent dividers sx={{ position: 'relative', minHeight: 360 }}>
                {loading && (
                    <Stack
                        spacing={1.5}
                        alignItems="center"
                        justifyContent="center"
                        sx={{ position: 'absolute', inset: 0, bgcolor: 'background.paper', opacity: 0.85 }}
                    >
                        <CircularProgress size={20} />
                        <Typography variant="body2">Loading chart...</Typography>
                    </Stack>
                )}
                {!loading && error && (
                    <Typography color="error" textAlign="center">
                        Error: {error}
                    </Typography>
                )}
                {!loading && !error && chartData && (
                    <Box sx={{ height: '100%', width: '100%' }}>
                        <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none" className="overflow-visible">
                            <line x1="0" y1="0" x2="100" y2="0" className="stroke-gray-200" strokeWidth="0.2" />
                            <line x1="0" y1="25" x2="100" y2="25" className="stroke-gray-200" strokeWidth="0.2" />
                            <line x1="0" y1="50" x2="100" y2="50" className="stroke-gray-200" strokeWidth="0.2" />
                            <line x1="0" y1="75" x2="100" y2="75" className="stroke-gray-200" strokeWidth="0.2" />
                            <line x1="0" y1="100" x2="100" y2="100" className="stroke-gray-200" strokeWidth="0.2" />

                            <path d={chartData.path} fill="none" className="stroke-blue-500" strokeWidth="0.5" strokeLinejoin="round" strokeLinecap="round" />

                            {chartData.points.map((point, i) => (
                                <g key={i}>
                                    <circle cx={point.x} cy={point.y} r="2" className="fill-blue-500 opacity-0 hover:opacity-100 transition-opacity" />
                                    <title>{`${point.temp.toFixed(1)}° at ${point.time.toLocaleString()}`}</title>
                                </g>
                            ))}

                            <text
                                x="-1"
                                y="0"
                                dy="0.3em"
                                textAnchor="end"
                                style={{
                                    fontSize: '4px',
                                    fill: theme.palette.text.secondary
                                }}
                            >
                                {chartData.maxTemp}°
                            </text>
                            <text
                                x="-1"
                                y="100"
                                dy="-0.1em"
                                textAnchor="end"
                                style={{
                                    fontSize: '4px',
                                    fill: theme.palette.text.secondary
                                }}
                            >
                                {chartData.minTemp}°
                            </text>
                            <text
                                x="0"
                                y="102"
                                dy="0.3em"
                                textAnchor="start"
                                style={{
                                    fontSize: '4px',
                                    fill: theme.palette.text.secondary
                                }}
                            >
                                {chartData.startTime.toLocaleDateString()}
                            </text>
                            <text
                                x="100"
                                y="102"
                                dy="0.3em"
                                textAnchor="end"
                                style={{
                                    fontSize: '4px',
                                    fill: theme.palette.text.secondary
                                }}
                            >
                                {chartData.endTime.toLocaleDateString()}
                            </text>
                        </svg>
                    </Box>
                )}
                {!loading && !error && !chartData && (
                    <Typography textAlign="center" color="text.secondary">
                        No data available for this period.
                    </Typography>
                )}
            </DialogContent>
        </Dialog>
    );
};
