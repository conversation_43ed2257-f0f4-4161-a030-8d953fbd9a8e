import React, { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import CircularProgress from '@mui/material/CircularProgress';
import { Species } from '../types';

interface AddProductModalProps {
  onClose: () => void;
  onSave: (newSpecies: Partial<Species>) => Promise<void>;
}

export const AddProductModal: React.FC<AddProductModalProps> = ({ onClose, onSave }) => {
  const [name, setName] = useState('');
  const [category, setCategory] = useState<'Fish' | 'Shellfish' | 'Other'>('Other');
  const [productForms, setProductForms] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    if (!name.trim() || isSaving) {
      return;
    }

    setIsSaving(true);

    try {
      await onSave({
        name,
        category,
        productForms: productForms.split(',').map(s => s.trim()).filter(Boolean),
      });
      onClose();
    } catch (error) {
      console.error('Failed to save product:', error);
      alert('Failed to save product. Please try again.');
      setIsSaving(false);
    }
  };

  return (
    <Dialog open onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add New Product</DialogTitle>
      <DialogContent dividers>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            label="Product Name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            autoFocus
          />
          <TextField
            select
            label="Category"
            value={category}
            onChange={(e) => setCategory(e.target.value as 'Fish' | 'Shellfish' | 'Other')}
          >
            <MenuItem value="Fish">Fish</MenuItem>
            <MenuItem value="Shellfish">Shellfish</MenuItem>
            <MenuItem value="Other">Other</MenuItem>
          </TextField>
          <TextField
            label="Product Forms (comma-separated)"
            value={productForms}
            onChange={(e) => setProductForms(e.target.value)}
            placeholder="e.g., Fillet, Whole, Portion"
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button onClick={handleSave} variant="contained" disabled={!name.trim() || isSaving} startIcon={isSaving ? <CircularProgress size={16} color="inherit" /> : undefined}>
          {isSaving ? 'Saving...' : 'Save Product'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
