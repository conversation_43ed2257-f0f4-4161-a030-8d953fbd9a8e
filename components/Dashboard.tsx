import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Chip,
  Stack,
  useTheme,
  Paper,
  Button,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
} from '@mui/material';
import { SelectChangeEvent } from '@mui/material/Select';
import { EmailContext, HACCPEvent, Species, Vendor, Location, EventType, UpdateEventField } from '../types';
import { PackageIcon, ShoppingCartIcon, TrashIcon, ClipboardIcon, ChartBarIcon, PencilSquareIcon, SparklesIcon, ThermometerIcon, ArchiveBoxIcon, FunnelIcon, UserCircleIcon, ClipboardDocumentListIcon, ChartPieIcon, TruckIcon } from './IconComponents';
import { InventoryDisplay } from './InventoryDisplay';
import { SurfaceCard, KpiCard, ChartCard } from './common/SurfaceCard';

interface DashboardProps {
    events: HACCPEvent[];
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onEditEvent: (event: HACCPEvent) => void;
    onDraftEmail: (context: EmailContext) => void;
    onUpdateEventField: UpdateEventField;
}

const eventTypeConfig: { [key in EventType]: { icon: React.ReactNode; label: string; eventTypeKey: keyof typeof import('../src/theme/tokens').eventTypePalette.light } } = {
    receiving: { icon: <PackageIcon />, label: 'Receiving', eventTypeKey: 'receiving' },
    sales: { icon: <ShoppingCartIcon />, label: 'Sales', eventTypeKey: 'sales' },
    disposal: { icon: <TrashIcon />, label: 'Disposal', eventTypeKey: 'disposal' },
    're-sealing': { icon: <ClipboardIcon />, label: 'Re-sealing', eventTypeKey: 'adjustment' },
    relocation: { icon: <TruckIcon />, label: 'Relocation', eventTypeKey: 'transfer' },
    sanitation: { icon: <SparklesIcon />, label: 'Sanitation', eventTypeKey: 'cleaning' },
    'thermometer-calibration': { icon: <ThermometerIcon />, label: 'Calibration', eventTypeKey: 'temperature' },
    inventory: { icon: <ArchiveBoxIcon />, label: 'Inventory', eventTypeKey: 'adjustment' },
    'employee-training': { icon: <UserCircleIcon />, label: 'Training', eventTypeKey: 'inspection' },
};

const EventTypeDisplay = ({ eventType }: { eventType: EventType }) => {
    const theme = useTheme();
    const config = eventTypeConfig[eventType] || { icon: null, label: eventType, eventTypeKey: 'receiving' as const };
    const { icon, label, eventTypeKey } = config;

    // Get colors from theme
    const eventColors = theme.palette.eventTypes?.[eventTypeKey] || {
        main: theme.palette.primary.main,
        light: theme.palette.primary.light,
        contrastText: theme.palette.primary.contrastText,
    };

    return (
        <Chip
            icon={<Box sx={{ width: 16, height: 16, color: eventColors.main }}>{icon}</Box>}
            label={label}
            size="small"
            sx={{
                bgcolor: eventColors.light,
                color: eventColors.main,
                '& .MuiChip-icon': {
                    color: eventColors.main,
                },
            }}
        />
    );
};

// KpiCard is now imported from SurfaceCard component

const EventPieChart: React.FC<{ data: { type: EventType; count: number; percentage: number; color: string }[] }> = ({ data }) => {
    const theme = useTheme();
    const radius = 60;
    const circumference = 2 * Math.PI * radius;
    let accumulatedOffset = 0;

    return (
        <ChartCard title="Event Distribution">
            <Box display="flex" flexDirection={{ xs: 'column', md: 'row' }} alignItems="center" height="100%">
                <Box position="relative" width={160} height={160}>
                    <svg viewBox="0 0 140 140" style={{ transform: 'rotate(-90deg)' }}>
                        {data.map(({ percentage, color }, index) => {
                            const strokeDashoffset = circumference - (percentage / 100) * circumference;
                            const rotation = (accumulatedOffset / 100) * 360;
                            accumulatedOffset += percentage;
                            return (
                                <circle
                                    key={index}
                                    r={radius}
                                    cx="70"
                                    cy="70"
                                    fill="transparent"
                                    stroke={color}
                                    strokeWidth="20"
                                    strokeDasharray={circumference}
                                    strokeDashoffset={strokeDashoffset}
                                    style={{
                                        transform: `rotate(${rotation}deg)`,
                                        transformOrigin: '70px 70px',
                                        transition: 'stroke-dashoffset 0.3s ease'
                                    }}
                                />
                            );
                        })}
                    </svg>
                </Box>
                <Box mt={{ xs: 2, md: 0 }} ml={{ xs: 0, md: 3 }} flex={1}>
                    <Stack spacing={1}>
                        {data.map(({ type, count, percentage, color }) => (
                            <Box key={type} display="flex" alignItems="center" justifyContent="space-between">
                                <Box display="flex" alignItems="center">
                                    <Box
                                        width={12}
                                        height={12}
                                        borderRadius="50%"
                                        bgcolor={color}
                                        mr={1.5}
                                    />
                                    <Typography variant="body2" color="text.secondary" sx={{ textTransform: 'capitalize' }}>
                                        {eventTypeConfig[type]?.label || type}
                                    </Typography>
                                </Box>
                                <Typography variant="body2" fontWeight="600" color="text.primary">
                                    {percentage.toFixed(1)}% ({count})
                                </Typography>
                            </Box>
                        ))}
                    </Stack>
                </Box>
            </Box>
        </ChartCard>
    );
};


const EventBarChart: React.FC<{ data: { date: string; count: number }[] }> = ({ data }) => {
    const theme = useTheme();
    const maxCount = Math.max(...data.map(d => d.count), 0) || 1;
    const labels = data.map(d => new Date(d.date + 'T00:00:00').toLocaleDateString('en-US', { weekday: 'short' }));

    return (
        <ChartCard title="Activity Last 7 Days">
            <Box display="flex" justifyContent="space-around" alignItems="flex-end" height={192} gap={1}>
                {data.map((item, index) => (
                    <Box key={index} display="flex" flexDirection="column" alignItems="center" flex={1}>
                        <Box
                            width="100%"
                            bgcolor={theme.palette.primary.main}
                            borderRadius="2px 2px 0 0"
                            sx={{
                                height: `${(item.count / maxCount) * 100}%`,
                                minHeight: item.count > 0 ? '4px' : '0px',
                                transition: 'height 0.3s ease',
                            }}
                            title={`${item.count} events on ${labels[index]}`}
                        />
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            {labels[index]}
                        </Typography>
                    </Box>
                ))}
            </Box>
        </ChartCard>
    );
};


const initialFilters = {
    startDate: '',
    endDate: '',
    eventTypes: [] as string[],
    species: [] as string[],
    vendors: [] as string[],
    locations: [] as string[],
    batchNumber: '',
};

type Filters = typeof initialFilters;

export const Dashboard: React.FC<DashboardProps> = ({ events, species, vendors, locations, onEditEvent, onDraftEmail, onUpdateEventField }) => {
    const [filters, setFilters] = useState<Filters>(initialFilters);
    const [showFilters, setShowFilters] = useState(false);
    const theme = useTheme();

    const handleFilterChange = <K extends keyof Filters>(filterName: K, value: Filters[K]) => {
        setFilters(prev => ({ ...prev, [filterName]: value }));
    };
    
    const handleMultiSelectChange = (filterName: 'eventTypes' | 'species' | 'vendors' | 'locations') => (event: SelectChangeEvent<string[]>) => {
        const { value } = event.target;
        handleFilterChange(filterName, typeof value === 'string' ? value.split(',') : value);
    };

    const filteredEvents = useMemo(() => {
        return events.filter(event => {
            if (filters.startDate && event.date < filters.startDate) return false;
            if (filters.endDate && event.date > filters.endDate) return false;
            if (filters.eventTypes.length > 0 && !filters.eventTypes.includes(event.eventType)) return false;
            if (filters.species.length > 0 && (!event.product || !filters.species.includes(event.product))) return false;
            if (filters.vendors.length > 0 && (!event.supplier || !filters.vendors.includes(event.supplier))) return false;
            if (filters.locations.length > 0 && (!event.location || !filters.locations.includes(event.location))) return false;
            if (filters.batchNumber && (!event.batchNumber || !event.batchNumber.toLowerCase().includes(filters.batchNumber.toLowerCase()))) return false;
            return true;
        });
    }, [events, filters]);
    
    const eventTypes = [...new Set(events.map(e => e.eventType))];
    const productRequiredEventTypes: EventType[] = ['receiving', 'sales', 'disposal', 're-sealing', 'inventory', 'relocation'];

    const kpiData = useMemo(() => {
        const today = new Date();
        const oneWeekAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 7);
        const oneMonthAgo = new Date(today);
        oneMonthAgo.setMonth(today.getMonth() - 1);
        
        const eventsLastWeek = events.filter(e => new Date(e.date) >= oneWeekAgo);
        const receivingLastMonth = events.filter(e => e.eventType === 'receiving' && new Date(e.date) >= oneMonthAgo);

        const eventTypeCounts = events.reduce((acc, event) => {
            acc[event.eventType] = (acc[event.eventType] || 0) + 1;
            return acc;
        }, {} as { [key: string]: number });
        
        const mostFrequent = Object.entries(eventTypeCounts).sort((a, b) => (b[1] as number) - (a[1] as number))[0];

        return {
            totalEventsLastWeek: eventsLastWeek.length,
            totalWeightReceived: receivingLastMonth.reduce((sum, e) => sum + (e.quantity || 0), 0),
            mostFrequentEventType: mostFrequent ? eventTypeConfig[mostFrequent[0] as EventType]?.label : 'N/A',
        };
    }, [events]);

    const chartData = useMemo(() => {
        // Pie Chart Data
        const eventTypeCounts = events.reduce((acc, event) => {
            acc[event.eventType] = (acc[event.eventType] || 0) + 1;
            return acc;
        }, {} as { [key: string]: number });

        const totalEvents = events.length;
        const pieChartData = Object.entries(eventTypeCounts)
            .map(([type, countValue]) => {
                const count = countValue as number;
                const config = eventTypeConfig[type as EventType];
                const eventTypeKey = config?.eventTypeKey || 'receiving';
                // Get color from theme's event type palette
                const eventColors = theme.palette.eventTypes?.[eventTypeKey];
                const color = eventColors?.main || theme.palette.primary.main;

                return {
                    type: type as EventType,
                    count,
                    percentage: totalEvents > 0 ? (count / totalEvents) * 100 : 0,
                    color,
                };
            })
            .sort((a, b) => b.count - a.count);

        // Bar Chart Data
        const last7Days = Array.from({ length: 7 }, (_, i) => {
            const d = new Date();
            d.setDate(d.getDate() - i);
            return d.toISOString().split('T')[0];
        }).reverse();
        
        const barChartData = last7Days.map(date => {
            const count = events.filter(e => e.date === date).length;
            return { date, count };
        });

        return { pieChartData, barChartData };
    }, [events]);


    return (
        <SurfaceCard
            sx={{
                maxWidth: '1200px',
                width: '100%',
                mx: 'auto',
                borderRadius: 3,
                boxShadow: theme.shadows[4],
            }}
            padding={4}
        >
            <Stack spacing={4}>
                <InventoryDisplay events={events} />

                {/* KPIs */}
                <Box
                    sx={{
                        display: 'grid',
                        gap: 3,
                        gridTemplateColumns: {
                            xs: 'repeat(1, minmax(0, 1fr))',
                            md: 'repeat(3, minmax(0, 1fr))',
                        },
                    }}
                >
                    <Box>
                        <KpiCard
                            title="Events This Week"
                            value={kpiData.totalEventsLastWeek.toString()}
                            icon={<Box component={ClipboardDocumentListIcon} sx={{ width: 24, height: 24 }} />}
                        />
                    </Box>
                    <Box>
                        <KpiCard
                            title="Receiving This Month"
                            value={`${kpiData.totalWeightReceived.toFixed(1)} lbs`}
                            icon={<Box component={PackageIcon} sx={{ width: 24, height: 24 }} />}
                        />
                    </Box>
                    <Box>
                        <KpiCard
                            title="Top Activity"
                            value={kpiData.mostFrequentEventType}
                            icon={<Box component={SparklesIcon} sx={{ width: 24, height: 24 }} />}
                        />
                    </Box>
                </Box>

                {/* Visualizations */}
                <Box
                    sx={{
                        display: 'grid',
                        gap: 3,
                        gridTemplateColumns: {
                            xs: 'repeat(1, minmax(0, 1fr))',
                            lg: 'repeat(2, minmax(0, 1fr))',
                        },
                    }}
                >
                    <Box>
                        <EventPieChart data={chartData.pieChartData} />
                    </Box>
                    <Box>
                        <EventBarChart data={chartData.barChartData} />
                    </Box>
                </Box>

                <Box>
                    <Box
                        component="header"
                        display="flex"
                        alignItems="center"
                        justifyContent="space-between"
                        mb={3}
                        pb={3}
                        sx={{ borderBottom: 1, borderColor: 'divider' }}
                    >
                        <Box display="flex" alignItems="center">
                            <Box
                                sx={{
                                    bgcolor: 'primary.light',
                                    color: 'primary.main',
                                    p: 1,
                                    borderRadius: 2,
                                    mr: 2,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                <ChartBarIcon sx={{ width: 24, height: 24 }} />
                            </Box>
                            <Typography variant="h5" fontWeight={700} color="text.primary">
                                Detailed Event History
                            </Typography>
                        </Box>
                    </Box>

                    <Paper
                        variant="outlined"
                        sx={{
                            p: 3,
                            mb: 3,
                            borderRadius: 2,
                            bgcolor: 'background.paper',
                        }}
                    >
                        <Button
                            onClick={() => setShowFilters(!showFilters)}
                            fullWidth
                            sx={{
                                justifyContent: 'space-between',
                                textTransform: 'none',
                                fontWeight: 600,
                                color: 'text.primary',
                            }}
                        >
                            <Box display="flex" alignItems="center">
                                <Box
                                    component="span"
                                    sx={{
                                        display: 'inline-flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mr: 1,
                                        color: 'primary.main',
                                    }}
                                >
                                    <Box component={FunnelIcon} sx={{ width: 20, height: 20 }} />
                                </Box>
                                Filter Events
                            </Box>
                            <Typography component="span" variant="body2" color="text.secondary">
                                {showFilters ? '▲' : '▼'}
                            </Typography>
                        </Button>
                        {showFilters && (
                            <Box
                                sx={{
                                    mt: 2,
                                    display: 'grid',
                                    gap: 2,
                                    gridTemplateColumns: {
                                        xs: 'repeat(1, minmax(0, 1fr))',
                                        sm: 'repeat(2, minmax(0, 1fr))',
                                        md: 'repeat(3, minmax(0, 1fr))',
                                        lg: 'repeat(4, minmax(0, 1fr))',
                                    },
                                }}
                            >
                                <Box>
                                    <TextField
                                        fullWidth
                                        type="date"
                                        label="Start Date"
                                        value={filters.startDate}
                                        onChange={e => handleFilterChange('startDate', e.target.value)}
                                        size="small"
                                        InputLabelProps={{ shrink: true }}
                                    />
                                </Box>
                                <Box>
                                    <TextField
                                        fullWidth
                                        type="date"
                                        label="End Date"
                                        value={filters.endDate}
                                        onChange={e => handleFilterChange('endDate', e.target.value)}
                                        size="small"
                                        InputLabelProps={{ shrink: true }}
                                    />
                                </Box>
                                <Box>
                                    <TextField
                                        fullWidth
                                        label="Batch Number"
                                        placeholder="Search batch..."
                                        value={filters.batchNumber}
                                        onChange={e => handleFilterChange('batchNumber', e.target.value)}
                                        size="small"
                                    />
                                </Box>
                                <Box>
                                    <FormControl fullWidth size="small">
                                        <InputLabel id="filter-event-types-label">Event Type</InputLabel>
                                        <Select
                                            labelId="filter-event-types-label"
                                            multiple
                                            value={filters.eventTypes}
                                            onChange={handleMultiSelectChange('eventTypes')}
                                            label="Event Type"
                                            renderValue={selected => (selected as string[]).join(', ')}
                                        >
                                            {eventTypes.map(type => (
                                                <MenuItem key={type} value={type}>
                                                    {eventTypeConfig[type as EventType]?.label || type}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Box>
                                <Box>
                                    <FormControl fullWidth size="small">
                                        <InputLabel id="filter-species-label">Species</InputLabel>
                                        <Select
                                            labelId="filter-species-label"
                                            multiple
                                            value={filters.species}
                                            onChange={handleMultiSelectChange('species')}
                                            label="Species"
                                            renderValue={selected => (selected as string[]).join(', ')}
                                        >
                                            {species.map(s => (
                                                <MenuItem key={s.id} value={s.name}>
                                                    {s.name}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Box>
                                <Box>
                                    <FormControl fullWidth size="small">
                                        <InputLabel id="filter-vendors-label">Vendors</InputLabel>
                                        <Select
                                            labelId="filter-vendors-label"
                                            multiple
                                            value={filters.vendors}
                                            onChange={handleMultiSelectChange('vendors')}
                                            label="Vendors"
                                            renderValue={selected => (selected as string[]).join(', ')}
                                        >
                                            {vendors.map(v => (
                                                <MenuItem key={v.id} value={v.name}>
                                                    {v.name}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Box>
                                <Box>
                                    <FormControl fullWidth size="small">
                                        <InputLabel id="filter-locations-label">Locations</InputLabel>
                                        <Select
                                            labelId="filter-locations-label"
                                            multiple
                                            value={filters.locations}
                                            onChange={handleMultiSelectChange('locations')}
                                            label="Locations"
                                            renderValue={selected => (selected as string[]).join(', ')}
                                        >
                                            {locations.map(l => (
                                                <MenuItem key={l.id} value={l.name}>
                                                    {l.name}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'flex-end' }}>
                                    <Button
                                        onClick={() => setFilters(initialFilters)}
                                        variant="outlined"
                                        fullWidth
                                        sx={{ fontWeight: 600 }}
                                    >
                                        Reset Filters
                                    </Button>
                                </Box>
                            </Box>
                        )}
                    </Paper>

                {filteredEvents.length === 0 ? (
                    <Typography variant="body2" sx={{ textAlign: 'center', color: 'text.secondary', py: 8 }}>
                        No events match the current filters.
                    </Typography>
                ) : (
                    <>
                        {/* Mobile Card View */}
                        <Stack spacing={1.5} sx={{ display: { xs: 'flex', md: 'none' } }}>
                            {filteredEvents.map((event) => {
                                const isProductMissing = productRequiredEventTypes.includes(event.eventType) && !event.product;
                                return (
                                <Paper key={event.id} variant="outlined" sx={{ p: 2 }}>
                                    <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 1 }}>
                                        <EventTypeDisplay eventType={event.eventType} />
                                        <Box sx={{ textAlign: 'right' }}>
                                            <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                                                {event.date}
                                            </Typography>
                                            <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                                                {event.time}
                                            </Typography>
                                        </Box>
                                    </Stack>
                                    <Box sx={{ fontWeight: 600, color: 'text.primary', mb: 1 }}>
                                    {isProductMissing ? (
                                        <FormControl fullWidth size="small">
                                            <Select
                                                defaultValue=""
                                                onChange={(e) => {
                                                    if (e.target.value) onUpdateEventField(event.id, 'product', e.target.value);
                                                }}
                                                displayEmpty
                                                sx={{ fontSize: '0.875rem' }}
                                            >
                                                <MenuItem value="" disabled>-- Select Product --</MenuItem>
                                                {species.map(s => <MenuItem key={s.id} value={s.name}>{s.name}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                    ) : (
                                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                            {event.product ? [event.product, event.productForm].filter(Boolean).join(' - ') : event.employeeName || 'N/A'}
                                        </Typography>
                                    )}
                                    </Box>
                                    <Box sx={{ mt: 1.5, pt: 1.5, borderTop: 1, borderColor: 'divider', display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 1 }}>
                                        {event.quantity && (
                                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                                <strong>Qty:</strong> {event.quantity.toFixed(2)} {event.unit}
                                            </Typography>
                                        )}
                                        {event.batchNumber && (
                                            <Typography variant="caption" sx={{ color: 'text.secondary', fontFamily: 'monospace' }}>
                                                <strong>Batch:</strong> {event.batchNumber}
                                            </Typography>
                                        )}
                                        {(event.supplier || event.fromLocation) && (
                                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                                <strong>From:</strong> {event.supplier || event.fromLocation}
                                            </Typography>
                                        )}
                                        {event.location && (
                                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                                <strong>To:</strong> {event.location}
                                            </Typography>
                                        )}
                                        {event.createdBy && (
                                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                                <strong>User:</strong> {event.createdBy}
                                            </Typography>
                                        )}
                                    </Box>
                                    <Stack direction="row" justifyContent="flex-end" spacing={1.5} sx={{ mt: 1.5, pt: 1.5, borderTop: 1, borderColor: 'divider' }}>
                                        {event.eventType === 'receiving' && (
                                            <Button
                                                onClick={() => onDraftEmail({type: 'receiving', event})}
                                                size="small"
                                                sx={{ fontSize: '0.75rem', fontWeight: 600 }}
                                            >
                                                EMAIL
                                            </Button>
                                        )}
                                        <Button
                                            onClick={() => onEditEvent(event)}
                                            size="small"
                                            startIcon={<PencilSquareIcon className="h-4 w-4" />}
                                            sx={{ fontSize: '0.875rem', fontWeight: 600 }}
                                        >
                                            Edit
                                        </Button>
                                    </Stack>
                                </Paper>
                            )})}
                        </Stack>
                        
                        {/* Desktop Table View */}
                        <TableContainer sx={{ display: { xs: 'none', md: 'block' } }}>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>Date</TableCell>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>Type</TableCell>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>Details</TableCell>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>Qty</TableCell>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>Batch #</TableCell>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>Vendor/From</TableCell>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>Location/To</TableCell>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>User</TableCell>
                                        <TableCell sx={{ bgcolor: 'background.surface', borderColor: 'divider', textTransform: 'uppercase', fontSize: '0.75rem', fontWeight: 600 }}>Actions</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {filteredEvents.map((event) => {
                                        const isProductMissing = productRequiredEventTypes.includes(event.eventType) && !event.product;
                                        return (
                                        <TableRow 
                                            key={event.id} 
                                            sx={{ 
                                                '&:hover': { bgcolor: 'action.hover' },
                                                '& td': { borderColor: 'divider' }
                                            }}
                                        >
                                            <TableCell>
                                                <Typography variant="body2" sx={{ color: 'text.primary', whiteSpace: 'nowrap' }}>
                                                    {event.date} {event.time}
                                                </Typography>
                                            </TableCell>
                                            <TableCell><EventTypeDisplay eventType={event.eventType} /></TableCell>
                                            <TableCell>
                                                {isProductMissing ? (
                                                    <FormControl size="small" fullWidth>
                                                        <Select
                                                            defaultValue=""
                                                            onChange={(e) => {
                                                                if (e.target.value) {
                                                                    onUpdateEventField(event.id, 'product', e.target.value);
                                                                }
                                                            }}
                                                            displayEmpty
                                                            aria-label={`Select product for event on ${event.date}`}
                                                            sx={{ fontSize: '0.875rem' }}
                                                        >
                                                            <MenuItem value="" disabled>-- Select --</MenuItem>
                                                            {species.map(s => (
                                                                <MenuItem key={s.id} value={s.name}>{s.name}</MenuItem>
                                                            ))}
                                                        </Select>
                                                    </FormControl>
                                                ) : (
                                                    <Typography variant="body2" sx={{ fontWeight: 600, whiteSpace: 'nowrap' }}>
                                                        {event.product ? [event.product, event.productForm].filter(Boolean).join(' - ') : event.employeeName || 'N/A'}
                                                    </Typography>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="body2" sx={{ color: 'text.secondary', whiteSpace: 'nowrap' }}>
                                                    {event.quantity ? `${event.quantity.toFixed(2)} ${event.unit}` : 'N/A'}
                                                </Typography>
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="body2" sx={{ color: 'text.secondary', fontFamily: 'monospace', fontSize: '0.75rem', whiteSpace: 'nowrap' }}>
                                                    {event.batchNumber || 'N/A'}
                                                </Typography>
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="body2" sx={{ color: 'text.secondary', whiteSpace: 'nowrap' }}>
                                                    {event.supplier || event.fromLocation || 'N/A'}
                                                </Typography>
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="body2" sx={{ color: 'text.secondary', whiteSpace: 'nowrap' }}>
                                                    {event.location || 'N/A'}
                                                </Typography>
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="body2" sx={{ color: 'text.secondary', whiteSpace: 'nowrap' }}>
                                                    {event.createdBy}
                                                </Typography>
                                            </TableCell>
                                            <TableCell>
                                                <Stack direction="row" spacing={1} alignItems="center">
                                                    <IconButton 
                                                        onClick={() => onEditEvent(event)} 
                                                        size="small"
                                                        title="Edit Event"
                                                        sx={{ color: 'primary.main' }}
                                                    >
                                                        <PencilSquareIcon className="h-5 w-5" />
                                                    </IconButton>
                                                    {event.eventType === 'receiving' && (
                                                        <Button
                                                            onClick={() => onDraftEmail({type: 'receiving', event})}
                                                            size="small"
                                                            title="Draft Receiving Email"
                                                            sx={{ fontSize: '0.75rem' }}
                                                        >
                                                            Email
                                                        </Button>
                                                    )}
                                                </Stack>
                                            </TableCell>
                                        </TableRow>
                                    )})}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </>
                )}
                </Box>
            </Stack>
        </SurfaceCard>
    );
};
