import React, { useState, useEffect } from 'react';
import {
  alpha,
  <PERSON>,
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typography,
  Alert,
  useTheme,
} from '@mui/material';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { db } from '../firebase/config';
import { Species } from '../types';
import { FishIcon, PlusIcon, PhotoIcon } from './IconComponents';
import { uploadImage } from '../utils/imageUtils';
import { docToPlainObject } from '../utils/firestoreUtils';
import { SurfaceCard } from './common/SurfaceCard';

export const SpeciesView: React.FC = () => {
  const theme = useTheme();
  const [speciesList, setSpeciesList] = useState<Species[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);

  const [editingId, setEditingId] = useState<string | null>(null);
  const [name, setName] = useState('');
  const [productForms, setProductForms] = useState('');
  const [qualityControlNotes, setQualityControlNotes] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  useEffect(() => {
    try {
      const speciesQuery = query(collection(db, 'species'), orderBy('name'));
      const unsubscribe = onSnapshot(
        speciesQuery,
        (snapshot: QuerySnapshot<DocumentData>) => {
          const list: Species[] = snapshot.docs.map((document) => docToPlainObject<Species>(document));
          setSpeciesList(list);
          setLoading(false);
        },
        (err) => {
          console.error(err);
          setError('Failed to fetch species data.');
          setLoading(false);
        },
      );
      return () => unsubscribe();
    } catch (err) {
      console.error(err);
      setError('Failed to initialize Firebase for species.');
      setLoading(false);
    }
  }, []);

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setImageFile(file);
      setImagePreview(URL.createObjectURL(file));
    }
  };

  const resetForm = () => {
    setEditingId(null);
    setName('');
    setProductForms('');
    setQualityControlNotes('');
    setImageFile(null);
    setImagePreview(null);
    setIsFormOpen(false);
  };

  const handleEdit = (species: Species) => {
    setEditingId(species.id);
    setName(species.name);
    setProductForms(species.productForms.join('\n'));
    setQualityControlNotes(species.qualityControlNotes);
    setImagePreview(species.imageUrl || null);
    setImageFile(null);
    setIsFormOpen(true);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!name || !qualityControlNotes) {
      alert('Species name and quality control notes are required.');
      return;
    }

    const formsArray = productForms.split('\n').map((value) => value.trim()).filter(Boolean);
    const speciesData: Partial<Species> = { name, productForms: formsArray, qualityControlNotes };

    if (imageFile) {
      try {
        const imageUrl = await uploadImage(imageFile, `species/${Date.now()}_${imageFile.name}`);
        speciesData.imageUrl = imageUrl;
      } catch (uploadError) {
        alert('Failed to upload image.');
        return;
      }
    } else if (editingId && imagePreview) {
      speciesData.imageUrl = imagePreview;
    }

    try {
      if (editingId) {
        const speciesRef = doc(db, 'species', editingId);
        await updateDoc(speciesRef, speciesData);
      } else {
        await addDoc(collection(db, 'species'), speciesData);
      }
      resetForm();
    } catch (saveError) {
      console.error('Error saving species: ', saveError);
      alert('Failed to save species data.');
    }
  };

  return (
    <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%' }}>
      <Stack spacing={3}>
        <Stack
          direction={{ xs: 'column', md: 'row' }}
          justifyContent="space-between"
          alignItems={{ xs: 'flex-start', md: 'center' }}
          spacing={2}
          sx={{ pb: 2, borderBottom: 1, borderColor: 'divider' }}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: alpha(theme.palette.success.main, 0.1),
                color: 'success.main',
                borderRadius: 2,
                p: 1.4,
              }}
            >
              <Box component={FishIcon} sx={{ width: 26, height: 26 }} />
            </Box>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>
              Species Information
            </Typography>
          </Stack>

          <Button
            variant="contained"
            startIcon={<Box component={PlusIcon} sx={{ width: 18, height: 18 }} />}
            onClick={() => {
              resetForm();
              setIsFormOpen(true);
            }}
            sx={{ textTransform: 'none' }}
          >
            Add Species
          </Button>
        </Stack>

        {isFormOpen && (
          <SurfaceCard sx={{ bgcolor: 'background.surface' }}>
            <Stack component="form" spacing={3} onSubmit={handleSubmit}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {editingId ? 'Edit Species' : 'Add New Species'}
              </Typography>

              <Stack spacing={2}>
                <TextField
                  label="Species Name"
                  value={name}
                  onChange={(event) => setName(event.target.value)}
                  required
                  fullWidth
                />
                <TextField
                  label="Product Forms (one per line)"
                  value={productForms}
                  onChange={(event) => setProductForms(event.target.value)}
                  multiline
                  minRows={3}
                  fullWidth
                />
                <TextField
                  label="Quality Control Violations & Notes"
                  value={qualityControlNotes}
                  onChange={(event) => setQualityControlNotes(event.target.value)}
                  required
                  multiline
                  minRows={4}
                  fullWidth
                />
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems={{ xs: 'flex-start', sm: 'center' }}>
                  <Box
                    sx={{
                      width: 96,
                      height: 96,
                      borderRadius: 2,
                      border: '1px dashed',
                      borderColor: 'divider',
                      bgcolor: 'background.paper',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      overflow: 'hidden',
                    }}
                  >
                    {imagePreview ? (
                      <Box component="img" src={imagePreview} alt="Species preview" sx={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                    ) : (
                      <Box component={PhotoIcon} sx={{ width: 36, height: 36, color: 'text.secondary' }} />
                    )}
                  </Box>
                  <Button variant="outlined" component="label" sx={{ textTransform: 'none' }}>
                    Upload Image
                    <input hidden accept="image/*" type="file" onChange={handleImageSelect} />
                  </Button>
                </Stack>
              </Stack>

              <Divider />

              <Stack direction="row" justifyContent="flex-end" spacing={2}>
                <Button variant="outlined" color="inherit" onClick={resetForm} sx={{ textTransform: 'none' }}>
                  Cancel
                </Button>
                <Button type="submit" variant="contained" sx={{ textTransform: 'none' }}>
                  {editingId ? 'Update Species' : 'Save Species'}
                </Button>
              </Stack>
            </Stack>
          </SurfaceCard>
        )}

        {loading && (
          <Typography variant="body2" color="text.secondary">
            Loading species…
          </Typography>
        )}

        {error && (
          <Alert severity="error" sx={{ borderRadius: 2 }}>
            {error}
          </Alert>
        )}

        {!loading && !error && (
          <Stack spacing={2.5}>
            {speciesList.length === 0 ? (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 4 }}>
                No species data added yet.
              </Typography>
            ) : (
              speciesList.map((species) => (
                <SurfaceCard key={species.id} sx={{ p: 3 }}>
                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2.5} alignItems={{ xs: 'flex-start', sm: 'stretch' }}>
                    {species.imageUrl && (
                      <Box
                        component="img"
                        src={species.imageUrl}
                        alt={species.name}
                        sx={{
                          width: 120,
                          height: 120,
                          borderRadius: 2,
                          objectFit: 'cover',
                          border: '1px solid',
                          borderColor: 'divider',
                          flexShrink: 0,
                        }}
                      />
                    )}
                    <Stack spacing={1.5} flex={1}>
                      <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {species.name}
                        </Typography>
                        <Button
                          onClick={() => handleEdit(species)}
                          size="small"
                          sx={{ textTransform: 'none' }}
                        >
                          Edit
                        </Button>
                      </Stack>
                      <Stack spacing={1}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Product Forms
                          </Typography>
                          <Typography variant="body2" color="text.primary">
                            {species.productForms.join(', ') || 'N/A'}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Quality Control Notes
                          </Typography>
                          <Typography variant="body2" color="text.primary" sx={{ whiteSpace: 'pre-wrap' }}>
                            {species.qualityControlNotes}
                          </Typography>
                        </Box>
                      </Stack>
                    </Stack>
                  </Stack>
                </SurfaceCard>
              ))
            )}
          </Stack>
        )}
      </Stack>
    </SurfaceCard>
  );
};