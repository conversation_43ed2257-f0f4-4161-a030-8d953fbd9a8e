
import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  Stack,
  useTheme,
} from '@mui/material';
import { HACCPEvent } from '../types';
import { CubeIcon } from './IconComponents';
import { SurfaceCard } from './common/SurfaceCard';

interface InventoryDisplayProps {
  events: HACCPEvent[];
}

export const InventoryDisplay: React.FC<InventoryDisplayProps> = ({ events }) => {
  const theme = useTheme();

  const inventory = useMemo(() => {
    const inventoryMap: { [product: string]: number } = {};

    // Sort events from oldest to newest to calculate inventory chronologically
    [...events].sort((a, b) => {
        const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return timeA - timeB;
    }).forEach(event => {
        if (!event.product || typeof event.quantity !== 'number') {
            return;
        }

        if (!inventoryMap[event.product]) {
            inventoryMap[event.product] = 0;
        }

        switch (event.eventType) {
            case 'receiving':
                inventoryMap[event.product] += event.quantity;
                break;
            case 'sales':
            case 'disposal':
                inventoryMap[event.product] -= event.quantity;
                break;
            default:
                break;
        }
    });

    // Filter out items with zero or negative quantity and format for display
    return Object.entries(inventoryMap)
        .filter(([, quantity]) => quantity > 0.01) // Use an epsilon for float comparison
        .map(([product, quantity]) => ({
            product,
            quantity,
        }))
        .sort((a, b) => a.product.localeCompare(b.product));
  }, [events]);

  return (
    <Box>
        {/* Header */}
        <Stack direction="row" alignItems="center" spacing={2} mb={3}>
            <Box
                sx={{
                    p: 1.5,
                    borderRadius: 2,
                    bgcolor: theme.palette.semantic?.success || theme.palette.success.light,
                    color: theme.palette.success.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
            >
                <Box component={CubeIcon} sx={{ width: 24, height: 24 }} />
            </Box>
            <Typography variant="h4" component="h1" fontWeight="bold" color="text.primary">
                Inventory Summary
            </Typography>
        </Stack>

        {inventory.length === 0 ? (
            <SurfaceCard sx={{ textAlign: 'center', py: 6 }}>
                <Typography variant="body1" color="text.secondary">
                    No inventory data available. Record a "Receiving" event to get started.
                </Typography>
            </SurfaceCard>
        ) : (
            <Box
                sx={{
                    display: 'grid',
                    gap: 2,
                    gridTemplateColumns: {
                        xs: 'repeat(2, minmax(0, 1fr))',
                        sm: 'repeat(3, minmax(0, 1fr))',
                        md: 'repeat(4, minmax(0, 1fr))',
                        lg: 'repeat(5, minmax(0, 1fr))',
                    },
                }}
            >
                {inventory.map(({ product, quantity }) => (
                    <SurfaceCard key={product} sx={{ textAlign: 'center', height: '100%' }}>
                        <Typography
                            variant="subtitle2"
                            fontWeight="600"
                            color="text.primary"
                            sx={{
                                mb: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                            }}
                            title={product}
                        >
                            {product}
                        </Typography>
                        <Typography
                            variant="h5"
                            fontWeight="bold"
                            color="primary.main"
                            sx={{ mb: 0.5 }}
                        >
                            {quantity.toFixed(2)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                            lbs
                        </Typography>
                    </SurfaceCard>
                ))}
            </Box>
        )}
    </Box>
  );
};
