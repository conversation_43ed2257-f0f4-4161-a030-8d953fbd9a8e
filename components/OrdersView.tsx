import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>po<PERSON>, Stack, Button, TextField, MenuItem, IconButton, useTheme, Snackbar, Alert } from '@mui/material';
import { alpha } from '@mui/material/styles';
import { db } from '../firebase/config';
import { collection, addDoc, updateDoc, doc, serverTimestamp, deleteDoc } from 'firebase/firestore';
import { PurchaseOrder, Vendor, Species, EmailContext } from '../types';
import { ClipboardDocumentListIcon, PlusIcon, TrashIcon } from './IconComponents';
import { SurfaceCard } from './common/SurfaceCard';

interface OrdersViewProps {
    purchaseOrders: PurchaseOrder[];
    vendors: Vendor[];
    species: Species[];
    onDraftEmail: (context: EmailContext) => void;
}

export const OrdersView: React.FC<OrdersViewProps> = ({ purchaseOrders, vendors, species, onDraftEmail }) => {
    const [isFormO<PERSON>, setIsFormOpen] = useState(false);
    
    // Form state
    const [vendorId, setVendorId] = useState('');
    const [speciesName, setSpeciesName] = useState('');
    const [quantity, setQuantity] = useState('');
    const [expectedDeliveryDate, setExpectedDeliveryDate] = useState(new Date().toISOString().split('T')[0]);
    
    // Error handling and feedback state
    const [pendingId, setPendingId] = useState<string | null>(null);
    const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });

    useEffect(() => {
        if (vendors.length > 0 && !vendorId) {
            setVendorId(vendors[0].id);
        }
        if (species.length > 0 && !speciesName) {
            setSpeciesName(species[0].name);
        }
    }, [vendors, species]);

    const resetForm = () => {
        setVendorId(vendors.length > 0 ? vendors[0].id : '');
        setSpeciesName(species.length > 0 ? species[0].name : '');
        setQuantity('');
        setExpectedDeliveryDate(new Date().toISOString().split('T')[0]);
        setIsFormOpen(false);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const selectedVendor = vendors.find(v => v.id === vendorId);
        if (!vendorId || !speciesName || !quantity || !expectedDeliveryDate || !selectedVendor) {
            alert("All fields are required.");
            return;
        }

        const orderData = { 
            vendorId,
            vendorName: selectedVendor.name,
            species: speciesName,
            quantity: parseFloat(quantity),
            unit: 'lbs',
            expectedDeliveryDate,
            status: 'planning',
            createdAt: serverTimestamp(),
        };

        try {
            await addDoc(collection(db, "purchaseOrders"), orderData);
            resetForm();
        } catch (error) {
            console.error("Error creating purchase order: ", error);
            alert("Failed to create purchase order.");
        }
    };
    
    const updateOrderStatus = async (orderId: string, status: 'ordered' | 'received') => {
        setPendingId(orderId);
        try {
            const orderRef = doc(db, 'purchaseOrders', orderId);
            await updateDoc(orderRef, { status });
            setSnackbar({ open: true, message: `Order marked as ${status}`, severity: 'success' });
        } catch (error) {
            console.error('Error updating order status:', error);
            setSnackbar({ open: true, message: 'Failed to update order. Please try again.', severity: 'error' });
        } finally {
            setPendingId(null);
        }
    };

    const deleteOrder = async (orderId: string) => {
        if (window.confirm("Are you sure you want to delete this planned order?")) {
            setPendingId(orderId);
            try {
                await deleteDoc(doc(db, 'purchaseOrders', orderId));
                setSnackbar({ open: true, message: 'Order deleted successfully', severity: 'success' });
            } catch (error) {
                console.error('Error deleting order:', error);
                setSnackbar({ open: true, message: 'Failed to delete order. Please try again.', severity: 'error' });
            } finally {
                setPendingId(null);
            }
        }
    };

    const today = new Date();
    const ordersToPlace = purchaseOrders.filter(o => {
        const vendor = vendors.find(v => v.id === o.vendorId);
        if (o.status !== 'planning' || !vendor?.sourcingLeadTimeDays) return false;
        const deliveryDate = new Date(o.expectedDeliveryDate + 'T00:00:00');
        const orderDate = new Date(deliveryDate);
        orderDate.setDate(deliveryDate.getDate() - vendor.sourcingLeadTimeDays);
        return today >= orderDate;
    });

    const plannedOrders = purchaseOrders.filter(o => o.status === 'planning' && !ordersToPlace.includes(o));
    const pendingOrders = purchaseOrders.filter(o => o.status === 'ordered');
    const receivedOrders = purchaseOrders.filter(o => o.status === 'received');

    const OrderCard: React.FC<{ order: PurchaseOrder, showActions?: boolean }> = ({ order, showActions = false }) => {
        const vendor = vendors.find(v => v.id === order.vendorId);
        const isPending = pendingId === order.id;
        
        return (
            <SurfaceCard>
                <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                    <Box>
                        <Typography variant="subtitle1" sx={{ color: 'text.primary', fontWeight: 'bold' }}>
                            {order.species}
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            {order.quantity} {order.unit}
                        </Typography>
                        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                            from {order.vendorName}
                        </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ color: 'text.primary', fontWeight: 600 }}>
                        Due: {order.expectedDeliveryDate}
                    </Typography>
                </Stack>
                {showActions && (
                    <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={1} sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider' }}>
                        <IconButton
                            onClick={() => deleteOrder(order.id)}
                            size="small"
                            disabled={isPending}
                            sx={{ color: 'error.main' }}
                        >
                            <TrashIcon className="h-5 w-5"/>
                        </IconButton>
                        {vendor && (
                            <Button
                                onClick={() => onDraftEmail({ type: 'order', order, vendor })}
                                size="small"
                                disabled={isPending}
                                sx={{ color: 'secondary.main', fontWeight: 600 }}
                            >
                                Draft Email
                            </Button>
                        )}
                        <Button
                            onClick={() => updateOrderStatus(order.id, 'ordered')}
                            size="small"
                            variant="contained"
                            disabled={isPending}
                        >
                            Mark as Ordered
                        </Button>
                    </Stack>
                )}
            </SurfaceCard>
        )
    };

    const theme = useTheme();

    return (
        <>
        <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%' }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ pb: 2, borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                <Stack direction="row" alignItems="center" spacing={2}>
                    <Box sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: 'warning.main', p: 1, borderRadius: 1 }}>
                        <ClipboardDocumentListIcon className="h-6 w-6" />
                    </Box>
                    <Typography variant="h5" component="h1" sx={{ color: 'text.primary', fontWeight: 'bold' }}>
                        Order Management
                    </Typography>
                </Stack>
                <Button 
                    onClick={() => setIsFormOpen(true)} 
                    variant="contained" 
                    startIcon={<PlusIcon className="h-5 w-5" />}
                >
                    Plan New Order
                </Button>
            </Stack>

            {isFormOpen && (
                <SurfaceCard sx={{ bgcolor: 'background.surface', p: 3, mb: 3 }}>
                    <Typography variant="h6" sx={{ color: 'text.primary', mb: 2, fontWeight: 600 }}>
                        Plan a New Purchase Order
                    </Typography>
                    <form onSubmit={handleSubmit}>
                        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)' }, gap: 2 }}>
                            <TextField
                                select
                                label="Vendor"
                                id="vendorId"
                                value={vendorId}
                                onChange={e => setVendorId(e.target.value)}
                                required
                                fullWidth
                            >
                                {vendors.map(v => <MenuItem key={v.id} value={v.id}>{v.name}</MenuItem>)}
                            </TextField>
                            <TextField
                                select
                                label="Species"
                                id="speciesName"
                                value={speciesName}
                                onChange={e => setSpeciesName(e.target.value)}
                                required
                                fullWidth
                            >
                                {species.map(s => <MenuItem key={s.id} value={s.name}>{s.name}</MenuItem>)}
                            </TextField>
                            <TextField
                                label="Quantity (lbs)"
                                id="quantity"
                                type="number"
                                value={quantity}
                                onChange={e => setQuantity(e.target.value)}
                                required
                                fullWidth
                            />
                            <TextField
                                label="Expected Delivery Date"
                                id="expectedDeliveryDate"
                                type="date"
                                value={expectedDeliveryDate}
                                onChange={e => setExpectedDeliveryDate(e.target.value)}
                                required
                                fullWidth
                                InputLabelProps={{ shrink: true }}
                            />
                        </Box>
                        <Stack direction="row" justifyContent="flex-end" spacing={2} sx={{ mt: 2 }}>
                            <Button type="button" onClick={resetForm} variant="outlined">Cancel</Button>
                            <Button type="submit" variant="contained">Save Plan</Button>
                        </Stack>
                    </form>
                </SurfaceCard>
            )}
            
            <Box sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' },
                gap: 3 
            }}>
                {/* Action Required */}
                <Box>
                    <Typography variant="h6" sx={{ color: 'error.main', mb: 2, fontWeight: 600 }}>
                        Action Required: Place Orders
                    </Typography>
                    <Stack spacing={2}>
                        {ordersToPlace.length > 0 ? ordersToPlace.map(o => <OrderCard key={o.id} order={o} showActions />) : (
                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                No orders require immediate action.
                            </Typography>
                        )}
                    </Stack>
                </Box>
                {/* Planned */}
                <Box>
                    <Typography variant="h6" sx={{ color: 'text.primary', mb: 2, fontWeight: 600 }}>
                        Planned Orders
                    </Typography>
                    <Stack spacing={2}>
                        {plannedOrders.length > 0 ? plannedOrders.map(o => <OrderCard key={o.id} order={o} showActions />) : (
                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                No orders currently planned.
                            </Typography>
                        )}
                    </Stack>
                </Box>
                {/* Pending */}
                <Box>
                    <Typography variant="h6" sx={{ color: 'text.primary', mb: 2, fontWeight: 600 }}>
                        Pending Delivery
                    </Typography>
                    <Stack spacing={2}>
                        {pendingOrders.length > 0 ? pendingOrders.map(o => <OrderCard key={o.id} order={o} />) : (
                            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                No orders pending delivery.
                            </Typography>
                        )}
                    </Stack>
                </Box>
            </Box>

        </SurfaceCard>
        
        <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
            <Alert
                onClose={() => setSnackbar({ ...snackbar, open: false })}
                severity={snackbar.severity}
                variant="filled"
                sx={{ width: '100%' }}
            >
                {snackbar.message}
            </Alert>
        </Snackbar>
        </>
    );
};