import React from 'react';
import { Chip, useTheme } from '@mui/material';
import { PackageIcon, ShoppingCartIcon, TrashIcon, ClipboardIcon, SparklesIcon, ThermometerIcon, ArchiveBoxIcon, UserCircleIcon, TruckIcon, CalendarIcon } from '../IconComponents';

interface EventTypeChipProps {
  eventType: string;
  size?: 'small' | 'medium';
}

const eventTypeConfig: Record<string, { icon: React.ReactNode; label: string }> = {
  receiving: { icon: <PackageIcon />, label: 'Receiving' },
  sales: { icon: <ShoppingCartIcon />, label: 'Sales' },
  disposal: { icon: <TrashIcon />, label: 'Disposal' },
  're-sealing': { icon: <ClipboardIcon />, label: 'Re-sealing' },
  relocation: { icon: <TruckIcon />, label: 'Relocation' },
  sanitation: { icon: <SparklesIcon />, label: 'Sanitation' },
  'thermometer-calibration': { icon: <ThermometerIcon />, label: 'Calibration' },
  inventory: { icon: <ArchiveBoxIcon />, label: 'Inventory' },
  'employee-training': { icon: <UserCircleIcon />, label: 'Training' },
};

/**
 * EventTypeChip - Theme-aware chip for displaying event types
 * Uses theme.palette.eventTypes for consistent coloring across light/dark modes
 */
export const EventTypeChip: React.FC<EventTypeChipProps> = ({ eventType, size = 'small' }) => {
  const theme = useTheme();
  
  const config = eventTypeConfig[eventType] || {
    icon: <CalendarIcon />,
    label: eventType,
  };

  // Map event types to theme palette keys
  const paletteKey = eventType === 're-sealing' ? 'adjustment' : 
                     eventType === 'thermometer-calibration' ? 'temperature' :
                     eventType === 'employee-training' ? 'inspection' :
                     eventType === 'sanitation' ? 'cleaning' :
                     eventType;

  const eventColors = (theme.palette as any).eventTypes?.[paletteKey] || {
    main: theme.palette.primary.main,
    light: theme.palette.primary.light,
    contrastText: theme.palette.primary.contrastText,
  };

  return (
    <Chip
      icon={<div style={{ width: 16, height: 16, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>{config.icon}</div>}
      label={config.label}
      size={size}
      sx={{
        bgcolor: eventColors.light,
        color: eventColors.main,
        fontWeight: 500,
        '& .MuiChip-icon': {
          color: eventColors.main,
        },
      }}
    />
  );
};
