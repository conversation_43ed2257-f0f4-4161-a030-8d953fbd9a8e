import React, { useMemo, useState } from 'react';
import {
  alpha,
  Box,
  Divider,
  IconButton,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import CloseIcon from '@mui/icons-material/Close';
import { HACCPEvent } from '../types';
import { CalendarIcon, ClockIcon, PlusIcon } from './IconComponents';
import { SurfaceCard } from './common/SurfaceCard';
import { EventTypeChip } from './common/EventTypeChip';

interface CalendarViewProps {
  events: HACCPEvent[];
  onAddNewEvent: (date: string) => void;
}

const EventModal: React.FC<{ date: string; events: HACCPEvent[]; onClose: () => void }> = ({ date, events, onClose }) => {
  const theme = useTheme();
  const formattedDate = new Date(`${date}T00:00:00`).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const sortedEvents = [...events].sort((a, b) => a.time.localeCompare(b.time));

  const renderDetails = (event: HACCPEvent) => {
    const baseDetails: Array<{ label: string; value: React.ReactNode }> = [];

    if (['receiving', 'sales', 'disposal', 're-sealing', 'inventory', 'relocation'].includes(event.eventType)) {
      if (event.quantity) {
        baseDetails.push({ label: 'Quantity', value: `${event.quantity.toFixed(2)} ${event.unit || ''}`.trim() });
      }
      if (event.batchNumber) {
        baseDetails.push({ label: 'Batch', value: <Typography component="span" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>{event.batchNumber}</Typography> });
      }
      if (event.supplier) {
        baseDetails.push({ label: 'Supplier', value: event.supplier });
      }
      if (event.temperature) {
        baseDetails.push({ label: 'Temperature', value: `${event.temperature}°F` });
      }
      if (event.fromLocation) {
        baseDetails.push({ label: 'From', value: event.fromLocation });
      }
      if (event.location) {
        baseDetails.push({ label: event.eventType === 'relocation' ? 'To' : 'Location', value: event.location });
      }
    }

    if (event.eventType === 'employee-training') {
      if (event.employeeName) {
        baseDetails.push({ label: 'Employee', value: event.employeeName });
      }
      if (event.trainingTopic) {
        baseDetails.push({ label: 'Topic', value: event.trainingTopic });
      }
    }

    if (event.eventType === 'sanitation') {
      if (event.areaCleaned) {
        baseDetails.push({ label: 'Area Cleaned', value: event.areaCleaned });
      }
      if (event.sanitizerUsed) {
        baseDetails.push({ label: 'Sanitizer Used', value: event.sanitizerUsed });
      }
    }

    if (event.eventType === 'thermometer-calibration') {
      if (event.thermometerId) {
        baseDetails.push({ label: 'Thermometer ID', value: event.thermometerId });
      }
      if (event.result) {
        baseDetails.push({
          label: 'Result',
          value: (
            <Typography component="span" sx={{ fontWeight: 700, color: event.result === 'pass' ? 'success.main' : 'error.main' }}>
              {event.result?.toUpperCase()}
            </Typography>
          ),
        });
      }
      if (event.calibrationMethod) {
        baseDetails.push({ label: 'Method', value: event.calibrationMethod });
      }
    }

    return baseDetails.length > 0 ? (
      <Stack spacing={0.5} sx={{ mt: 1 }}>
        {baseDetails.map(({ label, value }) => (
          <Typography key={label} variant="body2" color="text.secondary">
            <Typography component="span" variant="body2" sx={{ fontWeight: 600, color: 'text.primary', mr: 0.5 }}>
              {label}:
            </Typography>
            {value}
          </Typography>
        ))}
      </Stack>
    ) : null;
  };

  return (
    <Dialog
      open
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        variant: 'outlined',
        sx: { borderRadius: 3 },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          {formattedDate}
        </Typography>
        <IconButton onClick={onClose} edge="end" aria-label="Close event details dialog">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers sx={{ p: 3, bgcolor: 'background.default' }}>
        {sortedEvents.length === 0 ? (
          <Typography variant="body2" color="text.secondary">
            No events found for this date.
          </Typography>
        ) : (
          <Stack spacing={2}>
            {sortedEvents.map((event) => (
              <SurfaceCard key={event.id} padding={2.5} sx={{ bgcolor: 'background.paper', borderColor: 'divider' }}>
                <Stack spacing={2}>
                  <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                    <EventTypeChip eventType={event.eventType} />
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Box
                        component={ClockIcon}
                        sx={{ width: 16, height: 16, color: 'text.secondary', display: 'inline-flex' }}
                      />
                      <Typography variant="body2" color="text.secondary">
                        {event.time}
                      </Typography>
                    </Stack>
                  </Stack>

                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="flex-start">
                    {event.imageUrl && (
                      <Box
                        component="img"
                        src={event.imageUrl}
                        alt={event.imageDescription || 'Event image'}
                        sx={{
                          width: 120,
                          height: 120,
                          borderRadius: 2,
                          objectFit: 'cover',
                          border: '1px solid',
                          borderColor: 'divider',
                          flexShrink: 0,
                        }}
                      />
                    )}

                    <Box flex={1}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'text.primary' }}>
                        {[event.product, event.productForm].filter(Boolean).join(' - ') || event.employeeName || 'Untitled Event'}
                      </Typography>

                      {renderDetails(event)}
                    </Box>
                  </Stack>

                  {(event.imageDescription || event.correctiveAction || event.notes) && (
                    <Stack spacing={1.5}>
                      <Divider flexItem />
                      {event.imageDescription && (
                        <Typography variant="body2" color="text.secondary">
                          <Typography component="span" variant="body2" sx={{ fontWeight: 600, color: 'text.primary', mr: 0.5 }}>
                            AI Analysis:
                          </Typography>
                          {event.imageDescription}
                        </Typography>
                      )}
                      {event.correctiveAction && (
                        <Typography variant="body2" color="text.secondary">
                          <Typography component="span" variant="body2" sx={{ fontWeight: 600, color: 'text.primary', mr: 0.5 }}>
                            Corrective Action:
                          </Typography>
                          {event.correctiveAction}
                        </Typography>
                      )}
                      {event.notes && (
                        <Typography variant="body2" color="text.secondary">
                          <Typography component="span" variant="body2" sx={{ fontWeight: 600, color: 'text.primary', mr: 0.5 }}>
                            Notes:
                          </Typography>
                          {event.notes}
                        </Typography>
                      )}
                    </Stack>
                  )}
                </Stack>
              </SurfaceCard>
            ))}
          </Stack>
        )}
      </DialogContent>
    </Dialog>
  );
};

export const CalendarView: React.FC<CalendarViewProps> = ({ events, onAddNewEvent }) => {
  const theme = useTheme();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<string | null>(null);

  const eventsByDate = useMemo(() => {
    const map = new Map<string, HACCPEvent[]>();
    events.forEach((event) => {
      const dateStr = event.date;
      if (!map.has(dateStr)) {
        map.set(dateStr, []);
      }
      map.get(dateStr)!.push(event);
    });
    return map;
  }, [events]);

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();

  const daysInMonth = new Date(year, month + 1, 0).getDate();
  const firstDayOfMonth = new Date(year, month, 1).getDay();

  const changeMonth = (delta: number) => {
    setCurrentDate((prev) => {
      const next = new Date(prev);
      next.setMonth(prev.getMonth() + delta);
      return next;
    });
  };

  const dayCells: Array<number | null> = [
    ...Array.from({ length: firstDayOfMonth }, () => null),
    ...Array.from({ length: daysInMonth }, (_, index) => index + 1),
  ];

  const trailingPlaceholders = (7 - (dayCells.length % 7 || 7)) % 7;
  for (let i = 0; i < trailingPlaceholders; i += 1) {
    dayCells.push(null);
  }

  return (
    <SurfaceCard sx={{ p: { xs: 3, sm: 4 }, width: '100%' }}>
      <Stack spacing={3}>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          justifyContent="space-between"
          alignItems={{ xs: 'flex-start', sm: 'center' }}
          sx={{ pb: 2, borderBottom: 1, borderColor: 'divider' }}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: alpha(theme.palette.secondary.main, 0.1),
                color: 'secondary.main',
                borderRadius: 2,
                p: 1.5,
              }}
            >
              <Box component={CalendarIcon} sx={{ width: 28, height: 28 }} />
            </Box>
            <Typography variant="h5" sx={{ fontWeight: 700 }}>
              Event Calendar
            </Typography>
          </Stack>

          <Stack direction="row" spacing={1} alignItems="center">
            <IconButton onClick={() => changeMonth(-1)} aria-label="Previous month" color="primary" size="small">
              <ChevronLeftIcon />
            </IconButton>
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              {currentDate.toLocaleString('default', { month: 'long', year: 'numeric' })}
            </Typography>
            <IconButton onClick={() => changeMonth(1)} aria-label="Next month" color="primary" size="small">
              <ChevronRightIcon />
            </IconButton>
          </Stack>
        </Stack>

        <Box sx={{ border: 1, borderColor: 'divider', borderRadius: 2, overflow: 'hidden' }}>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(7, 1fr)',
              bgcolor: 'background.surface',
            }}
          >
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <Box
                key={day}
                sx={{
                  borderRight: '1px solid',
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                  textAlign: 'center',
                  py: 1,
                }}
              >
                <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary', textTransform: 'uppercase' }}>
                  {day}
                </Typography>
              </Box>
            ))}
          </Box>

          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(7, 1fr)',
            }}
          >
            {dayCells.map((day, index) => {
              if (!day) {
                return (
                  <Box
                    key={`blank-${index}`}
                    sx={{
                      borderRight: '1px solid',
                      borderBottom: '1px solid',
                      borderColor: 'divider',
                      minHeight: { xs: 80, sm: 96 },
                    }}
                  />
                );
              }

              const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
              const dayEvents = eventsByDate.get(dateStr) || [];
              const hasEvents = dayEvents.length > 0;
              const isToday = new Date().toDateString() === new Date(year, month, day).toDateString();

              return (
                <Box
                  key={`day-${day}`}
                  sx={{
                    position: 'relative',
                    borderRight: '1px solid',
                    borderBottom: '1px solid',
                    borderColor: 'divider',
                    minHeight: { xs: 80, sm: 96 },
                    p: 1.25,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    transition: theme.transitions.create('background-color', { duration: theme.transitions.duration.shorter }),
                    cursor: hasEvents ? 'pointer' : 'default',
                    '& .MuiIconButton-root': {
                      opacity: 0,
                      transition: theme.transitions.create('opacity', { duration: theme.transitions.duration.shortest }),
                    },
                    '&:hover': hasEvents
                      ? { bgcolor: 'action.hover' }
                      : { '& .MuiIconButton-root': { opacity: 1 } },
                    '&:hover .MuiIconButton-root': {
                      opacity: 1,
                    },
                  }}
                  onClick={() => hasEvents && setSelectedDate(dateStr)}
                >
                  <Typography
                    component="time"
                    variant="subtitle2"
                    sx={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: 32,
                      height: 32,
                      borderRadius: '999px',
                      fontWeight: isToday ? 700 : 600,
                      bgcolor: isToday ? 'primary.main' : 'transparent',
                      color: isToday ? 'primary.contrastText' : 'text.secondary',
                    }}
                  >
                    {day}
                  </Typography>

                  <IconButton
                    size="small"
                    color="primary"
                    aria-label={`Add new event for ${dateStr}`}
                    onClick={(event) => {
                      event.stopPropagation();
                      onAddNewEvent(dateStr);
                    }}
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      bgcolor: alpha(theme.palette.primary.main, 0.08),
                      '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.16) },
                      '& svg': { width: 16, height: 16 },
                    }}
                  >
                    <Box component={PlusIcon} sx={{ width: 16, height: 16 }} />
                  </IconButton>

                  {hasEvents && (
                    <Stack direction="row" spacing={0.75} sx={{ mt: 'auto' }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: 'primary.main',
                        }}
                      />
                      {dayEvents.length > 1 && (
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            bgcolor: 'success.main',
                          }}
                        />
                      )}
                      {dayEvents.length > 2 && (
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            bgcolor: 'error.main',
                          }}
                        />
                      )}
                    </Stack>
                  )}
                </Box>
              );
            })}
          </Box>
        </Box>
      </Stack>

      {selectedDate && (
        <EventModal
          date={selectedDate}
          events={eventsByDate.get(selectedDate) || []}
          onClose={() => setSelectedDate(null)}
        />
      )}
    </SurfaceCard>
  );
};
