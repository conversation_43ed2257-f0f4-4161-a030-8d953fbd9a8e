import React, { useState, useCallback, useMemo, useEffect } from 'react';
import * as xlsx from 'xlsx';
import { GoogleGenAI, Type } from '@google/genai';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Typography, Stack, Paper, TextField, Chip, Alert, CircularProgress, FormControl, InputLabel, Select, MenuItem, useTheme } from '@mui/material';
import { alpha } from '@mui/material/styles';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import DeleteIcon from '@mui/icons-material/Delete';
import { HACCPEvent, Species, Vendor, Location, EventType } from '../types';
import { TrashIcon } from './IconComponents';

interface ImportModalProps {
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onClose: () => void;
    onSave: (events: Partial<HACCPEvent>[]) => void;
}

type ModalStep = 'upload' | 'loading' | 'confirm' | 'error';

type TempHACCPEvent = Partial<HACCPEvent> & { tempId: number };

export const ImportModal: React.FC<ImportModalProps> = ({ species, vendors, locations, onClose, onSave }) => {
    const [step, setStep] = useState<ModalStep>('upload');
    const [originalEvents, setOriginalEvents] = useState<TempHACCPEvent[]>([]);
    const [extractedEvents, setExtractedEvents] = useState<TempHACCPEvent[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [overrideEventType, setOverrideEventType] = useState<EventType | 'auto'>('auto');

    const handleFile = useCallback(async (file: File) => {
        setStep('loading');
        setError(null);
        setOverrideEventType('auto');
        try {
            const data = await file.arrayBuffer();
            const workbook = xlsx.read(data);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const json: any[] = xlsx.utils.sheet_to_json(worksheet);

            if (json.length === 0) {
                setError("The uploaded file is empty or in an unsupported format.");
                setStep('error');
                return;
            }

            const headers = Object.keys(json[0]).join(', ');
            const fullDataString = json.map(row => JSON.stringify(row)).join('\n');
            
            const ai = new GoogleGenAI({ apiKey: process.env.API_KEY as string });
            
            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash',
                contents: { parts: [{ text: `
                    Analyze the following data extracted from a user's spreadsheet and convert it into a JSON array of HACCP events.
                    
                    File Headers: ${headers}
                    Spreadsheet Data:
                    ${fullDataString}

                    Existing database values for matching:
                    - Species: ${species.map(s => s.name).join(', ')}
                    - Vendors: ${vendors.map(v => v.name).join(', ')}
                    - Locations: ${locations.map(l => l.name).join(', ')}

                    Your task is to map the spreadsheet columns to the fields in the provided JSON schema.
                    - Infer the 'eventType' from the data (e.g., if there's a supplier, it's likely 'receiving'). Default to 'inventory' if unsure.
                    - If a date is present, use it in YYYY-MM-DD format. Standardize date formats if possible (e.g., MM/DD/YY -> YYYY-MM-DD). If no year is present assume the current year.
                    - Ensure 'quantity' and 'temperature' are numbers.
                    - Be precise. If a value for a field isn't in the data, omit that key from the object.
                    - Your output MUST be ONLY the JSON array.
                `}] },
                config: {
                    responseMimeType: 'application/json',
                    responseSchema: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.OBJECT,
                            properties: {
                                eventType: { type: Type.STRING, enum: ['receiving', 'inventory', 'disposal', 'sales', 'sanitation', 'relocation', 're-sealing'] },
                                product: { type: Type.STRING, description: 'The name of the seafood species.' },
                                productForm: { type: Type.STRING, description: 'The form of the product, e.g., Fillet, Whole.' },
                                quantity: { type: Type.NUMBER, description: 'The quantity of the product.' },
                                unit: { type: Type.STRING, description: 'The unit of measurement, e.g., lbs.' },
                                supplier: { type: Type.STRING, description: 'The name of the vendor or supplier.' },
                                location: { type: Type.STRING, description: 'The storage location.' },
                                date: { type: Type.STRING, description: 'The date of the event in YYYY-MM-DD format.' },
                                batchNumber: { type: Type.STRING, description: 'The batch or lot number.' },
                                temperature: { type: Type.NUMBER, description: 'The temperature in Fahrenheit.' },
                                notes: { type: Type.STRING, description: 'Any other relevant notes from the document.' },
                            },
                        },
                    },
                },
            });
            
            const parsedEvents = JSON.parse(response.text).map((event: Partial<HACCPEvent>, index: number) => ({
                ...event,
                tempId: index,
            }));
            setOriginalEvents(parsedEvents);
            setExtractedEvents(parsedEvents);
            setStep('confirm');

        } catch (err: any) {
            console.error("Error processing file:", err);
            if (err.message?.includes('does not have permission') || err.message?.includes('not found')) {
                setError("API Key Error. Please close and re-select your API key from the main screen.");
            } else {
                setError("AI analysis failed. Please ensure the file is a standard CSV or Excel format and try again.");
            }
            setStep('error');
        }
    }, [species, vendors, locations]);
    
    useEffect(() => {
        if (step !== 'confirm' || originalEvents.length === 0) return;

        if (overrideEventType === 'auto') {
            setExtractedEvents(currentEvents =>
                currentEvents.map(event => {
                    const originalEvent = originalEvents.find(o => o.tempId === event.tempId);
                    return {
                        ...event,
                        eventType: originalEvent ? originalEvent.eventType : 'inventory',
                    };
                })
            );
        } else {
            setExtractedEvents(currentEvents =>
                currentEvents.map(event => ({
                    ...event,
                    eventType: overrideEventType,
                }))
            );
        }
    }, [overrideEventType, originalEvents, step]);

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFile(e.target.files[0]);
        }
    };
    
    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragging(false);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFile(e.dataTransfer.files[0]);
        }
    };

    const handleDataChange = (id: number, field: keyof HACCPEvent, value: string | number) => {
        setExtractedEvents(currentEvents =>
            currentEvents.map(event =>
                event.tempId === id ? { ...event, [field]: value } : event
            )
        );
    };

    const removeEvent = (id: number) => {
        setExtractedEvents(prev => prev.filter(event => event.tempId !== id));
        setOriginalEvents(prev => prev.filter(event => event.tempId !== id));
    };

    const { newSpecies, newVendors, newLocations } = useMemo(() => {
        const existingSpecies = new Set(species.map(s => s.name.toLowerCase()));
        const existingVendors = new Set(vendors.map(v => v.name.toLowerCase()));
        const existingLocations = new Set(locations.map(l => l.name.toLowerCase()));
        const newSpecies = new Set<string>();
        const newVendors = new Set<string>();
        const newLocations = new Set<string>();
        extractedEvents.forEach(event => {
            if (event.product && !existingSpecies.has(event.product.toLowerCase())) newSpecies.add(event.product);
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) newVendors.add(event.supplier);
            if (event.location && !existingLocations.has(event.location.toLowerCase())) newLocations.add(event.location);
        });
        return { newSpecies: [...newSpecies], newVendors: [...newVendors], newLocations: [...newLocations] };
    }, [extractedEvents, species, vendors, locations]);

    const theme = useTheme();
    
    const renderContent = () => {
        switch (step) {
            case 'upload':
                return (
                    <Box sx={{ p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', textAlign: 'center', height: '100%' }}>
                        <Paper
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            variant="outlined"
                            sx={{
                                width: '100%',
                                p: 5,
                                border: '2px dashed',
                                borderColor: isDragging ? 'primary.main' : 'divider',
                                bgcolor: isDragging ? (t) => alpha(t.palette.primary.main, 0.08) : 'transparent',
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                transition: 'all 0.2s',
                            }}
                        >
                            <UploadFileIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                Drag & drop your CSV or Excel file here
                            </Typography>
                            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
                                or
                            </Typography>
                            <Button variant="contained" component="label">
                                Browse Files
                                <input id="file-upload" type="file" hidden onChange={handleFileSelect} accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
                            </Button>
                        </Paper>
                        <Typography variant="caption" sx={{ color: 'text.secondary', mt: 2 }}>
                            Your data is processed locally in your browser.
                        </Typography>
                    </Box>
                );
            case 'loading':
                return (
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        <CircularProgress size={64} />
                        <Typography variant="h6" sx={{ mt: 2, color: 'text.primary' }}>
                            Parsing file & analyzing data...
                        </Typography>
                    </Box>
                );
            case 'error':
                 return (
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%', textAlign: 'center', p: 3 }}>
                        <Alert severity="error" sx={{ mb: 3, maxWidth: 600 }}>
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>An Error Occurred</Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>{error}</Typography>
                        </Alert>
                        <Button onClick={() => setStep('upload')} variant="contained">
                            Try Again
                        </Button>
                    </Box>
                );
            case 'confirm':
                const hasNewItems = newSpecies.length > 0 || newVendors.length > 0 || newLocations.length > 0;
                return (
                    <Box sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
                        <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 2, color: 'text.primary' }}>
                            Confirm Imported Data
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                            <FormControl fullWidth size="small">
                                <InputLabel id="eventTypeOverride-label">Set all events to type</InputLabel>
                                <Select
                                    labelId="eventTypeOverride-label"
                                    id="eventTypeOverride"
                                    value={overrideEventType}
                                    label="Set all events to type"
                                    onChange={e => setOverrideEventType(e.target.value as EventType | 'auto')}
                                >
                                    <MenuItem value="auto">Auto-Detect (from file)</MenuItem>
                                    <MenuItem value="receiving">Receiving</MenuItem>
                                    <MenuItem value="sales">Sales</MenuItem>
                                    <MenuItem value="disposal">Disposal</MenuItem>
                                    <MenuItem value="relocation">Relocation</MenuItem>
                                    <MenuItem value="re-sealing">Re-sealing</MenuItem>
                                    <MenuItem value="inventory">Inventory Check</MenuItem>
                                    <MenuItem value="sanitation">Sanitation</MenuItem>
                                </Select>
                            </FormControl>
                        </Paper>
                        {hasNewItems && (
                            <Alert severity="info" sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>New items detected!</Typography>
                                <Typography variant="body2">These will be added to your database upon saving:</Typography>
                                <Box component="ul" sx={{ pl: 2, mt: 0.5, fontSize: '0.875rem' }}>
                                    {newSpecies.length > 0 && <li><strong>Species:</strong> {newSpecies.join(', ')}</li>}
                                    {newVendors.length > 0 && <li><strong>Vendors:</strong> {newVendors.join(', ')}</li>}
                                    {newLocations.length > 0 && <li><strong>Locations:</strong> {newLocations.join(', ')}</li>}
                                </Box>
                            </Alert>
                        )}
                        <Stack spacing={2}>
                           {extractedEvents.map(event => {
                                const isNewProduct = event.product && newSpecies.includes(event.product);
                                const isNewSupplier = event.supplier && newVendors.includes(event.supplier);
                                const isNewLocation = event.location && newLocations.includes(event.location);

                                return (
                                <Paper key={event.tempId} variant="outlined" sx={{ p: 2, position: 'relative' }}>
                                    <IconButton
                                        onClick={() => removeEvent(event.tempId)}
                                        size="small"
                                        sx={{ position: 'absolute', top: 8, right: 8, color: 'text.secondary', '&:hover': { color: 'error.main' } }}
                                    >
                                        <DeleteIcon fontSize="small" />
                                    </IconButton>
                                    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }, gap: 2 }}>
                                        <TextField
                                            label="Event"
                                            value={event.eventType || ''}
                                            onChange={e => handleDataChange(event.tempId, 'eventType', e.target.value)}
                                            disabled={overrideEventType !== 'auto'}
                                            size="small"
                                            fullWidth
                                        />
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Product"
                                                value={event.product || ''}
                                                onChange={e => handleDataChange(event.tempId, 'product', e.target.value)}
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewProduct && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <TextField
                                            label="Quantity"
                                            type="number"
                                            value={event.quantity || ''}
                                            onChange={e => handleDataChange(event.tempId, 'quantity', parseFloat(e.target.value))}
                                            size="small"
                                            fullWidth
                                        />
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Supplier"
                                                value={event.supplier || ''}
                                                onChange={e => handleDataChange(event.tempId, 'supplier', e.target.value)}
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewSupplier && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <Box sx={{ position: 'relative' }}>
                                            <TextField
                                                label="Location"
                                                value={event.location || ''}
                                                onChange={e => handleDataChange(event.tempId, 'location', e.target.value)}
                                                size="small"
                                                fullWidth
                                            />
                                            {isNewLocation && <Chip label="New" color="success" size="small" sx={{ position: 'absolute', top: -8, right: -8 }} />}
                                        </Box>
                                        <TextField
                                            label="Date"
                                            type="date"
                                            value={event.date || ''}
                                            onChange={e => handleDataChange(event.tempId, 'date', e.target.value)}
                                            size="small"
                                            fullWidth
                                            InputLabelProps={{ shrink: true }}
                                        />
                                        <TextField
                                            label="Notes"
                                            value={event.notes || ''}
                                            onChange={e => handleDataChange(event.tempId, 'notes', e.target.value)}
                                            size="small"
                                            fullWidth
                                            sx={{ gridColumn: { xs: '1', sm: 'span 2', md: 'span 3' } }}
                                        />
                                    </Box>
                                </Paper>
                                );
                           })}
                        </Stack>
                    </Box>
                );
            default: return null;
        }
    };
    
    const handleModalClose = useCallback(() => {
        setStep('upload');
        setOriginalEvents([]);
        setExtractedEvents([]);
        setError(null);
        onClose();
    }, [onClose]);
    
    return (
        <Dialog
            open
            onClose={handleModalClose}
            maxWidth="lg"
            fullWidth
            PaperProps={{ sx: { bgcolor: 'background.default' } }}
        >
            <DialogTitle
                sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', borderBottom: '1px solid', borderColor: 'divider' }}
            >
                Import from File
                <IconButton onClick={handleModalClose} edge="end" aria-label="Close import dialog">
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent sx={{ p: 0 }}>
                <Box sx={{ flexGrow: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
                    {renderContent()}
                </Box>
            </DialogContent>
            {step === 'confirm' && (
                <DialogActions sx={{ borderTop: '1px solid', borderColor: 'divider', px: 3, py: 2 }}>
                    <Button onClick={handleModalClose} color="inherit">
                        Cancel
                    </Button>
                    <Button onClick={() => onSave(extractedEvents)} variant="contained" disabled={extractedEvents.length === 0}>
                        Save {extractedEvents.length} Items
                    </Button>
                </DialogActions>
            )}
        </Dialog>
    );
};
