import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Box, IconButton, Avatar, Typography, Stack, List, ListItemButton, ListItemIcon, ListItemText, alpha, useTheme } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import Icon from '@mui/material/Icon';

const SIDEBAR_STORAGE_KEY = 'sidebar-collapsed';
const SIDEBAR_ID = 'main-sidebar';

const getFocusableElements = (container: HTMLElement): HTMLElement[] => {
  const selectors = ['button', '[href]', 'input', 'select', 'textarea', '[tabindex]:not([tabindex="-1"])'];
  return Array.from(container.querySelectorAll<HTMLElement>(selectors.join(','))).filter(
    (element) => !element.hasAttribute('disabled') && element.getAttribute('aria-hidden') !== 'true',
  );
};

export type SidebarView =
  | 'dashboard'
  | 'lot-tracking'
  | 'inventory'
  | 'temperature'
  | 'vendors'
  | 'species'
  | 'locations'
  | 'orders'
  | 'haccp'
  | 'calendar'
  | 'reports'
  | 'settings';

interface SidebarProps {
  currentView: SidebarView;
  onNavigate: (view: SidebarView) => void;
  onCollapseChange?: (collapsed: boolean) => void;
  collapsed?: boolean;
}

const navItems = [
  { key: 'dashboard' as SidebarView, icon: 'dashboard', label: 'Dashboard' },
  { key: 'lot-tracking' as SidebarView, icon: 'pin_drop', label: 'Lot Tracking' },
  { key: 'inventory' as SidebarView, icon: 'inventory_2', label: 'Inventory' },
  { key: 'temperature' as SidebarView, icon: 'device_thermostat', label: 'Temperature' },
  { key: 'vendors' as SidebarView, icon: 'storefront', label: 'Vendors' },
  { key: 'species' as SidebarView, icon: 'set_meal', label: 'Species' },
  { key: 'locations' as SidebarView, icon: 'place', label: 'Locations' },
  { key: 'orders' as SidebarView, icon: 'shopping_cart', label: 'Orders' },
  { key: 'haccp' as SidebarView, icon: 'health_and_safety', label: 'HACCP Logs' },
  { key: 'calendar' as SidebarView, icon: 'calendar_month', label: 'Calendar' },
  { key: 'reports' as SidebarView, icon: 'assessment', label: 'Reports' },
  { key: 'settings' as SidebarView, icon: 'settings', label: 'Settings' },
];

export const Sidebar: React.FC<SidebarProps> = ({ currentView, onNavigate, onCollapseChange, collapsed: collapsedProp }) => {
  const [internalCollapsed, setInternalCollapsed] = useState(() => {
    if (typeof window === 'undefined') {
      return true;
    }
    try {
      const stored = window.localStorage.getItem(SIDEBAR_STORAGE_KEY);
      if (stored !== null) {
        return JSON.parse(stored) as boolean;
      }
    } catch (error) {
      // ignore storage errors
    }
    return window.innerWidth < 768;
  });
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [isMobileViewport, setIsMobileViewport] = useState(() =>
    typeof window !== 'undefined' ? window.innerWidth < 768 : false,
  );
  const [liveMessage, setLiveMessage] = useState('');

  const toggleButtonRef = useRef<HTMLButtonElement>(null);
  const sidebarRef = useRef<HTMLElement>(null);
  const previouslyFocusedRef = useRef<HTMLElement | null>(null);

  const effectiveCollapsed = collapsedProp ?? internalCollapsed;

  const setCollapsed = useCallback(
    (nextCollapsed: boolean) => {
      onCollapseChange?.(nextCollapsed);
      if (collapsedProp === undefined) {
        setInternalCollapsed(nextCollapsed);
      }
    },
    [collapsedProp, onCollapseChange],
  );

  const focusFirstInteractive = useCallback(() => {
    const sidebarElement = sidebarRef.current;
    if (!sidebarElement) {
      return;
    }
    const focusableElements = getFocusableElements(sidebarElement);
    if (focusableElements.length) {
      focusableElements[0].focus();
    }
  }, []);

  const handleToggleCollapse = () => {
    const nextCollapsed = !effectiveCollapsed;
    if (nextCollapsed) {
      previouslyFocusedRef.current = document.activeElement as HTMLElement | null;
    }
    setCollapsed(nextCollapsed);
    if (nextCollapsed) {
      toggleButtonRef.current?.focus();
    } else {
      setTimeout(() => {
        focusFirstInteractive();
      }, 0);
    }
  };

  const theme = useTheme();
  
  return (
    <>
      {/* Mobile backdrop overlay */}
      {!effectiveCollapsed && (
        <Box
          sx={{
            display: { xs: 'block', md: 'none' },
            position: 'fixed',
            inset: 0,
            bgcolor: (t) => alpha(t.palette.common.black, 0.5),
            zIndex: (t) => t.zIndex.drawer - 1,
          }}
          onClick={() => setCollapsed(true)}
        />
      )}

      <Box
        ref={sidebarRef}
        component="aside"
        id="main-navigation"
        sx={{
          position: 'fixed',
          left: 0,
          top: 0,
          height: '100vh',
          width: {
            xs: effectiveCollapsed ? 0 : 256,
            md: effectiveCollapsed ? 64 : 256,
          },
          bgcolor: 'background.paper',
          borderRight: 1,
          borderColor: 'divider',
          zIndex: (t) => t.zIndex.drawer,
          transition: (t) => t.transitions.create(['width', 'transform'], {
            duration: t.transitions.duration.standard,
          }),
          transform: {
            xs: effectiveCollapsed ? 'translateX(-100%)' : 'translateX(0)',
            md: 'translateX(0)',
          },
          display: 'flex',
          flexDirection: 'column',
          p: 2,
          overflowX: 'hidden',
        }}
      >
        {/* Hamburger menu button */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <IconButton
            ref={toggleButtonRef}
            onClick={handleToggleCollapse}
            title={effectiveCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            aria-label={effectiveCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            sx={{
              color: 'text.primary',
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
          >
            <MenuIcon />
          </IconButton>
        </Box>

        {/* Company logo section */}
        <Stack spacing={2} sx={{ mb: 4, overflow: 'hidden' }}>
          {effectiveCollapsed ? (
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <Avatar
                src="https://lh3.googleusercontent.com/aida-public/AB6AXuD62ZxrENcl7YNKEE-vAvUj-yiHmqQnR7RnjPLN9dlQftGZlrzUiSjqj3a3eoWOnLNGJoPZbxGz5ES_LAi5qsNwTPfQKTUXwuQUwvmR1JluCvopQlCZo-t0EAkDtnxDNMfWbqPZbni5-WQk5xsZHwbk86ldgad0_ToTjBnaNAHO3mj8gqENo-CCfdmoWe9Cd7ntzrc4Ej0a3lbduE1rOGC3xt-R4bfOo200ocVxd3pa6fDzGVWb82vg3d-aXIRJACwkYW_Ao14zu9k"
                alt="Company logo for Atlantic Seafoods"
                sx={{ width: 40, height: 40 }}
              />
            </Box>
          ) : (
            <Stack direction="row" spacing={1.5} alignItems="center" sx={{ whiteSpace: 'nowrap' }}>
              <Avatar
                src="https://lh3.googleusercontent.com/aida-public/AB6AXuD62ZxrENcl7YNKEE-vAvUj-yiHmqQnR7RnjPLN9dlQftGZlrzUiSjqj3a3eoWOnLNGJoPZbxGz5ES_LAi5qsNwTPfQKTUXwuQUwvmR1JluCvopQlCZo-t0EAkDtnxDNMfWbqPZbni5-WQk5xsZHwbk86ldgad0_ToTjBnaNAHO3mj8gqENo-CCfdmoWe9Cd7ntzrc4Ej0a3lbduE1rOGC3xt-R4bfOo200ocVxd3pa6fDzGVWb82vg3d-aXIRJACwkYW_Ao14zu9k"
                alt="Company logo for Atlantic Seafoods"
                sx={{ width: 40, height: 40 }}
              />
              <Box>
                <Typography variant="body1" sx={{ color: 'text.primary', fontWeight: 'bold' }}>
                  Atlantic Seafoods
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  Admin
                </Typography>
              </Box>
            </Stack>
          )}
        </Stack>

        {/* Navigation */}
        <List component="nav" sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          {navItems.map((item) => (
            <ListItemButton
              key={item.key}
              selected={currentView === item.key}
              onClick={() => onNavigate(item.key)}
              title={effectiveCollapsed ? item.label : undefined}
              sx={{
                borderRadius: 1.5,
                minHeight: 40,
                px: effectiveCollapsed ? 1 : 1.5,
                justifyContent: effectiveCollapsed ? 'center' : 'flex-start',
                '&.Mui-selected': {
                  bgcolor: 'action.selected',
                  '&:hover': {
                    bgcolor: 'action.selected',
                  },
                },
                '&:hover': {
                  bgcolor: 'action.hover',
                },
              }}
            >
              <ListItemIcon sx={{ minWidth: effectiveCollapsed ? 'auto' : 36, color: 'inherit' }}>
                <Icon className="material-symbols-outlined">{item.icon}</Icon>
              </ListItemIcon>
              {!effectiveCollapsed && (
                <ListItemText
                  primary={item.label}
                  primaryTypographyProps={{
                    variant: 'body2',
                    fontWeight: currentView === item.key ? 'bold' : 'medium',
                  }}
                />
              )}
            </ListItemButton>
          ))}
        </List>
      </Box>
    </>
  );
};
