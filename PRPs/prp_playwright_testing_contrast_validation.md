name: "PRP - Playwright Component Testing with Contrast Validation & Design Guidelines"
description: "Automated testing framework using <PERSON><PERSON> to validate all components for WCAG contrast requirements in both light and dark modes, plus enforcement of UI design standards"

---

## Goal

**Feature Goal**: Establish automated Playwright testing infrastructure that validates all UI components for WCAG AA contrast requirements in both light and dark modes, while enforcing strict adherence to the repository's UI design standards documented in [`UI_STANDARDS.md`](./ai_docs/UI_STANDARDS.md).

**Deliverable**: 
- Playwright test suite with comprehensive component coverage
- Automated contrast ratio validation for light/dark themes
- Design pattern compliance checks
- CI/CD integration ready test configuration
- Detailed test report with actionable recommendations

**Success Definition**: 
- All components tested in both light and dark modes
- WCAG AA contrast ratios (4.5:1 for text, 3:1 for UI components) validated
- Zero critical UI_STANDARDS violations detected
- Test suite runs in < 5 minutes
- Clear, actionable report generated with specific file/line recommendations

## User Persona

**Target User**: Developers (AI and human) working in this repository

**Use Case**: Before committing code changes, run automated tests to ensure:
- New components meet contrast requirements
- Existing components maintain accessibility standards
- Design patterns follow established guidelines
- No regressions in theme adaptation

**User Journey**: 
1. <PERSON><PERSON><PERSON> creates/modifies component
2. Runs `npm run test:playwright` locally
3. Reviews generated report with specific violations
4. Fixes issues based on actionable recommendations
5. Re-runs tests to verify fixes
6. Commits with confidence

**Pain Points Addressed**: 
- Manual contrast checking is time-consuming and error-prone
- Design pattern violations discovered late in review process
- Inconsistent theme adaptation across components
- No automated enforcement of UI standards

## Why

- Ensures WCAG AA accessibility compliance across entire application
- Prevents theme adaptation regressions (addresses issue from [`prp_theme_adaptation.md`](./prp_theme_adaptation.md))
- Automates enforcement of [`UI_STANDARDS.md`](./ai_docs/UI_STANDARDS.md) rules
- Provides objective, measurable quality metrics
- Reduces manual code review burden
- Catches issues before they reach production

## What

Create a Playwright-based testing framework that:

1. **Component Discovery**: Automatically identifies all React components in the codebase
2. **Theme Testing**: Tests each component in both light and dark modes
3. **Contrast Validation**: Measures contrast ratios for all text and interactive elements
4. **Design Pattern Checks**: Validates adherence to UI_STANDARDS.md rules
5. **Report Generation**: Produces detailed HTML report with:
   - Component-by-component contrast scores
   - Specific violations with file paths and line numbers
   - Before/after screenshots
   - Actionable fix recommendations
   - Global design pattern compliance score

### Success Criteria

- [ ] Playwright configured with TypeScript support
- [ ] All major components tested (Dashboard, Sidebar, Forms, Modals, etc.)
- [ ] Contrast ratios validated against WCAG AA standards
- [ ] Light and dark mode screenshots captured for each component
- [ ] Design pattern violations detected and reported
- [ ] Test execution time < 5 minutes
- [ ] HTML report generated with actionable recommendations
- [ ] CI/CD integration documentation provided
- [ ] Zero false positives in violation detection

## All Needed Context

### Context Completeness Check

This PRP provides complete context for implementing Playwright testing with contrast validation. It includes:
- Existing theme system structure
- Component architecture patterns
- Design standards to enforce
- Contrast calculation methodology
- Report generation requirements

### Documentation & References

```yaml
- file: PRPs/ai_docs/UI_STANDARDS.md
  why: Complete list of design patterns and violations to detect
  pattern: Automated scan patterns for each rule category
  critical: Section 3 (THEMING) and Section 6 (ACCESSIBILITY) are highest priority

- file: src/theme.ts
  why: Theme structure and color token definitions
  pattern: Access palette values for contrast calculations
  gotcha: Uses MUI's cssVariables with colorSchemeSelector

- file: src/theme/tokens.ts
  why: Semantic color definitions for both light and dark modes
  pattern: eventTypePalette, semanticSurfaces, textContrasts, borderColors
  gotcha: Colors defined as separate light/dark objects

- file: components/Dashboard.tsx
  why: Example of complex component with multiple theme-aware elements
  pattern: Uses theme tokens, MUI components, and custom styling
  gotcha: Mix of Tailwind classes (lines 332-519) that need validation

- file: components/Sidebar.tsx
  why: Example of component with dark mode classes
  pattern: Uses Tailwind dark: variants
  gotcha: Custom color classes that may not follow theme tokens

- file: App.tsx
  why: Main application structure and theme provider setup
  pattern: AppThemeProvider wraps entire app
  gotcha: Voice assistant overlay (lines 1854-1878) needs contrast validation

- url: https://playwright.dev/docs/test-components
  why: Playwright component testing documentation
  critical: Setup for React component testing

- url: https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html
  why: WCAG contrast requirements specification
  critical: 4.5:1 for normal text, 3:1 for large text and UI components

- url: https://github.com/bbc/a11y-tests-web
  why: Reference implementation for automated accessibility testing
  pattern: Contrast calculation and reporting patterns
</yaml>

### Current Codebase Tree

```bash
.
├── components/
│   ├── Dashboard.tsx              # Complex component with charts, KPIs, tables
│   ├── Sidebar.tsx                # Navigation with theme-aware styling
│   ├── CalendarView.tsx           # Calendar with event type colors
│   ├── EmailDraftModal.tsx        # Modal with form elements
│   ├── FreezerLayoutModal.tsx     # Modal with interactive layout
│   ├── InventoryDisplay.tsx       # Cards with product data
│   ├── TemperatureView.tsx        # Charts and temperature data
│   ├── common/
│   │   └── SurfaceCard.tsx        # Reusable card component
│   └── LotTracking/
│       └── *.tsx                  # Lot tracking components
├── src/
│   ├── theme.ts                   # MUI theme configuration
│   ├── theme/
│   │   └── tokens.ts              # Color token definitions
│   ├── AppThemeProvider.tsx       # Theme provider with mode toggle
│   └── layouts/
│       └── DashboardLayout.tsx    # Main layout wrapper
├── PRPs/
│   └── ai_docs/
│       └── UI_STANDARDS.md        # Design standards to enforce
└── package.json
```

### Desired Codebase Tree

```bash
.
├── tests/
│   ├── playwright/
│   │   ├── config/
│   │   │   └── playwright.config.ts          # Playwright configuration
│   │   ├── fixtures/
│   │   │   ├── theme-fixture.ts              # Theme switching fixture
│   │   │   └── contrast-fixture.ts           # Contrast calculation fixture
│   │   ├── utils/
│   │   │   ├── contrast-calculator.ts        # WCAG contrast ratio calculator
│   │   │   ├── color-extractor.ts            # Extract colors from elements
│   │   │   ├── ui-standards-validator.ts     # Validate against UI_STANDARDS.md
│   │   │   └── report-generator.ts           # Generate HTML reports
│   │   ├── specs/
│   │   │   ├── components/
│   │   │   │   ├── dashboard.spec.ts         # Dashboard component tests
│   │   │   │   ├── sidebar.spec.ts           # Sidebar tests
│   │   │   │   ├── modals.spec.ts            # All modal tests
│   │   │   │   ├── forms.spec.ts             # Form component tests
│   │   │   │   └── cards.spec.ts             # Card component tests
│   │   │   ├── contrast/
│   │   │   │   ├── light-mode.spec.ts        # Light mode contrast validation
│   │   │   │   └── dark-mode.spec.ts         # Dark mode contrast validation
│   │   │   └── design-patterns/
│   │   │       ├── tailwind-v4.spec.ts       # Tailwind v4 compliance
│   │   │       ├── responsive.spec.ts        # Responsive design checks
│   │   │       ├── theming.spec.ts           # Theme consistency checks
│   │   │       ├── radix-ui.spec.ts          # Radix UI pattern checks
│   │   │       └── accessibility.spec.ts     # A11y pattern checks
│   │   └── reports/
│   │       ├── templates/
│   │       │   └── report-template.html      # HTML report template
│   │       └── .gitkeep
│   └── README.md                              # Testing documentation
├── package.json                               # Updated with Playwright scripts
└── playwright.config.ts                       # Root Playwright config
```

### Known Gotchas

```typescript
// CRITICAL: MUI theme uses data-mui-color-scheme attribute, not class="dark"
// Playwright must switch themes via: document.documentElement.setAttribute('data-mui-color-scheme', 'dark')

// CRITICAL: Contrast calculation must account for alpha transparency
// Use computed styles, not raw CSS values

// CRITICAL: Some components use Tailwind classes that don't respond to theme toggle
// These need to be flagged as violations (see Dashboard.tsx lines 332-519)

// CRITICAL: Voice assistant overlay uses alpha() for transparency
// Must calculate effective contrast against actual background

// CRITICAL: Event type colors come from theme.palette.eventTypes
// Must validate all event type color combinations

// CRITICAL: Playwright component testing requires React 18+ and proper setup
// May need to mock Firebase and other external dependencies
```

## Implementation Blueprint

### Data Models and Structure

```typescript
// tests/playwright/types.ts

export interface ContrastResult {
  element: string;
  selector: string;
  foreground: string;
  background: string;
  ratio: number;
  required: number;
  passes: boolean;
  wcagLevel: 'AA' | 'AAA';
  elementType: 'text' | 'large-text' | 'ui-component';
  location: {
    file: string;
    line?: number;
  };
}

export interface ComponentTestResult {
  component: string;
  lightMode: {
    screenshot: string;
    contrastResults: ContrastResult[];
    violations: DesignViolation[];
  };
  darkMode: {
    screenshot: string;
    contrastResults: ContrastResult[];
    violations: DesignViolation[];
  };
  overallScore: number;
}

export interface DesignViolation {
  rule: string;
  severity: 'critical' | 'high' | 'medium';
  description: string;
  location: {
    file: string;
    line?: number;
    selector?: string;
  };
  recommendation: string;
  autoFixable: boolean;
}

export interface TestReport {
  timestamp: string;
  duration: number;
  components: ComponentTestResult[];
  summary: {
    totalComponents: number;
    totalViolations: number;
    criticalViolations: number;
    contrastFailures: number;
    overallScore: number;
  };
  recommendations: string[];
}
```

### Implementation Tasks

```yaml
Task 1: CREATE tests/playwright/config/playwright.config.ts
  - IMPLEMENT: Playwright configuration with TypeScript, React component testing
  - PATTERN: Use @playwright/experimental-ct-react for component testing
  - NAMING: Standard Playwright config structure
  - DEPENDENCIES: Install @playwright/test, @playwright/experimental-ct-react
  - PLACEMENT: Root of tests/playwright/config/
  - VALIDATE: npx playwright test --config=tests/playwright/config/playwright.config.ts --list

Task 2: CREATE tests/playwright/utils/contrast-calculator.ts
  - IMPLEMENT: WCAG contrast ratio calculation (luminance-based)
  - PATTERN: Follow WCAG 2.1 formula: (L1 + 0.05) / (L2 + 0.05)
  - NAMING: calculateContrast(fg: string, bg: string): number
  - DEPENDENCIES: None (pure calculation)
  - PLACEMENT: tests/playwright/utils/
  - VALIDATE: Unit tests for known color pairs (white/black = 21:1)

Task 3: CREATE tests/playwright/utils/color-extractor.ts
  - IMPLEMENT: Extract computed colors from DOM elements
  - PATTERN: Use page.evaluate() to get window.getComputedStyle()
  - NAMING: extractColors(element: Locator): Promise<{fg: string, bg: string}>
  - DEPENDENCIES: Playwright Locator API
  - GOTCHA: Handle alpha transparency by compositing with parent backgrounds
  - PLACEMENT: tests/playwright/utils/
  - VALIDATE: Test with known elements from Dashboard

Task 4: CREATE tests/playwright/fixtures/theme-fixture.ts
  - IMPLEMENT: Fixture to switch between light and dark modes
  - PATTERN: Extend base test with theme switching capability
  - NAMING: test.extend<{ theme: 'light' | 'dark' }>
  - DEPENDENCIES: Playwright fixtures API
  - GOTCHA: Must set data-mui-color-scheme attribute, not class
  - PLACEMENT: tests/playwright/fixtures/
  - VALIDATE: Verify theme switch affects computed styles

Task 5: CREATE tests/playwright/utils/ui-standards-validator.ts
  - IMPLEMENT: Validate components against UI_STANDARDS.md rules
  - PATTERN: Parse component source, check for violations
  - NAMING: validateComponent(componentPath: string): DesignViolation[]
  - DEPENDENCIES: TypeScript AST parser (ts-morph or @typescript-eslint/parser)
  - CRITICAL: Implement checks for:
    * Dynamic class construction (Section 1)
    * Missing dark: variants (Section 3)
    * Non-responsive grids (Section 2)
    * Missing keyboard support (Section 6)
    * Hardcoded colors (Section 3)
  - PLACEMENT: tests/playwright/utils/
  - VALIDATE: Test against known violations in Dashboard.tsx

Task 6: CREATE tests/playwright/specs/contrast/light-mode.spec.ts
  - IMPLEMENT: Test all components in light mode for contrast
  - PATTERN: Mount component, extract colors, calculate ratios
  - NAMING: describe('Light Mode Contrast', () => { test('Component', ...) })
  - DEPENDENCIES: theme-fixture, contrast-calculator, color-extractor
  - CRITICAL: Test all text elements, buttons, inputs, chips, cards
  - PLACEMENT: tests/playwright/specs/contrast/
  - VALIDATE: Run against Dashboard, verify known passing elements

Task 7: CREATE tests/playwright/specs/contrast/dark-mode.spec.ts
  - IMPLEMENT: Test all components in dark mode for contrast
  - PATTERN: Same as light mode but with theme='dark'
  - NAMING: describe('Dark Mode Contrast', () => { test('Component', ...) })
  - DEPENDENCIES: theme-fixture, contrast-calculator, color-extractor
  - CRITICAL: Validate voice assistant overlay contrast
  - PLACEMENT: tests/playwright/specs/contrast/
  - VALIDATE: Run against Dashboard, verify dark mode specific elements

Task 8: CREATE tests/playwright/specs/components/dashboard.spec.ts
  - IMPLEMENT: Comprehensive Dashboard component tests
  - PATTERN: Test KPIs, charts, tables, filters in both themes
  - NAMING: describe('Dashboard Component', () => { ... })
  - DEPENDENCIES: All fixtures and utils
  - CRITICAL: Validate event type chip colors, chart segment colors
  - PLACEMENT: tests/playwright/specs/components/
  - VALIDATE: Ensure all Dashboard elements tested

Task 9: CREATE tests/playwright/specs/design-patterns/tailwind-v4.spec.ts
  - IMPLEMENT: Detect dynamic class construction violations
  - PATTERN: Scan component source for template literals with ${...}
  - NAMING: describe('Tailwind v4 Compliance', () => { ... })
  - DEPENDENCIES: ui-standards-validator
  - CRITICAL: Flag bg-${color}, ring-${color}, etc.
  - PLACEMENT: tests/playwright/specs/design-patterns/
  - VALIDATE: Detect known violations in Dashboard filter section

Task 10: CREATE tests/playwright/specs/design-patterns/accessibility.spec.ts
  - IMPLEMENT: Validate keyboard support, ARIA attributes, focus rings
  - PATTERN: Test interactive elements for proper attributes
  - NAMING: describe('Accessibility Patterns', () => { ... })
  - DEPENDENCIES: Playwright accessibility testing
  - CRITICAL: Check icon-only buttons for aria-label
  - PLACEMENT: tests/playwright/specs/design-patterns/
  - VALIDATE: Test Sidebar navigation links

Task 11: CREATE tests/playwright/utils/report-generator.ts
  - IMPLEMENT: Generate HTML report from test results
  - PATTERN: Aggregate all test results, render HTML template
  - NAMING: generateReport(results: TestReport): Promise<void>
  - DEPENDENCIES: fs/promises for file writing
  - CRITICAL: Include screenshots, violation details, recommendations
  - PLACEMENT: tests/playwright/utils/
  - VALIDATE: Generate sample report, verify all sections present

Task 12: CREATE tests/playwright/reports/templates/report-template.html
  - IMPLEMENT: HTML template for test report
  - PATTERN: Responsive design, sortable tables, expandable sections
  - NAMING: Standard HTML structure
  - CRITICAL: Include:
    * Executive summary with scores
    * Component-by-component breakdown
    * Screenshot comparisons (light/dark)
    * Violation list with file paths
    * Actionable recommendations
  - PLACEMENT: tests/playwright/reports/templates/
  - VALIDATE: Open in browser, verify all interactive elements work

Task 13: UPDATE package.json
  - IMPLEMENT: Add Playwright scripts and dependencies
  - PATTERN: Standard npm scripts
  - SCRIPTS:
    * "test:playwright": "playwright test"
    * "test:playwright:ui": "playwright test --ui"
    * "test:playwright:report": "playwright show-report"
    * "test:playwright:contrast": "playwright test tests/playwright/specs/contrast"
  - DEPENDENCIES:
    * "@playwright/test": "^1.40.0"
    * "@playwright/experimental-ct-react": "^1.40.0"
    * "ts-morph": "^21.0.0"
  - PLACEMENT: Root package.json
  - VALIDATE: npm install, verify all scripts run

Task 14: CREATE tests/README.md
  - IMPLEMENT: Comprehensive testing documentation
  - PATTERN: Include setup, usage, CI/CD integration
  - SECTIONS:
    * Quick start
    * Running tests locally
    * Understanding reports
    * Adding new tests
    * CI/CD integration
    * Troubleshooting
  - PLACEMENT: tests/
  - VALIDATE: Follow instructions to verify completeness
```

### Implementation Patterns & Key Details

```typescript
// Contrast calculation pattern (WCAG 2.1)
function calculateContrast(fg: string, bg: string): number {
  const getLuminance = (rgb: [number, number, number]): number => {
    const [r, g, b] = rgb.map(val => {
      const sRGB = val / 255;
      return sRGB <= 0.03928
        ? sRGB / 12.92
        : Math.pow((sRGB + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };

  const fgLum = getLuminance(parseRGB(fg));
  const bgLum = getLuminance(parseRGB(bg));
  const lighter = Math.max(fgLum, bgLum);
  const darker = Math.min(fgLum, bgLum);
  
  return (lighter + 0.05) / (darker + 0.05);
}

// Theme switching pattern
test.extend<{ theme: 'light' | 'dark' }>({
  theme: async ({ page }, use, testInfo) => {
    const theme = testInfo.project.name.includes('dark') ? 'dark' : 'light';
    await page.evaluate((mode) => {
      document.documentElement.setAttribute('data-mui-color-scheme', mode);
    }, theme);
    await use(theme);
  },
});

// Component testing pattern
test('Dashboard KPIs have sufficient contrast', async ({ mount, theme }) => {
  const component = await mount(<Dashboard {...props} />);
  
  const kpiCards = component.locator('[data-testid="kpi-card"]');
  const count = await kpiCards.count();
  
  for (let i = 0; i < count; i++) {
    const card = kpiCards.nth(i);
    const title = card.locator('h6');
    const value = card.locator('p');
    
    const titleColors = await extractColors(title);
    const valueColors = await extractColors(value);
    
    const titleContrast = calculateContrast(titleColors.fg, titleColors.bg);
    const valueContrast = calculateContrast(valueColors.fg, valueColors.bg);
    
    expect(titleContrast).toBeGreaterThanOrEqual(4.5);
    expect(valueContrast).toBeGreaterThanOrEqual(4.5);
  }
});

// Design pattern validation
async function validateTailwindV4(componentPath: string): Promise<DesignViolation[]> {
  const source = await fs.readFile(componentPath, 'utf-8');
  const violations: DesignViolation[] = [];
  
  // Check for dynamic class construction
  const dynamicClassRegex = /className.*`.*\$\{.*\}`/g;
  const matches = source.matchAll(dynamicClassRegex);
  
  for (const match of matches) {
    violations.push({
      rule: 'Tailwind v4: No dynamic class construction',
      severity: 'critical',
      description: 'Dynamic class construction breaks Tailwind purge',
      location: { file: componentPath, line: getLineNumber(source, match.index) },
      recommendation: 'Use static lookup object instead',
      autoFixable: false,
    });
  }
  
  return violations;
}
```

### Integration Points

```yaml
CI/CD:
  - GitHub Actions workflow: .github/workflows/playwright.yml
  - Run on: pull_request, push to main
  - Artifacts: HTML report, screenshots
  - Fail on: Critical violations or contrast failures

Pre-commit Hook:
  - Run contrast tests on changed components only
  - Fast feedback loop (< 30 seconds)
  - Block commit if critical violations found

Development:
  - npm run test:playwright:ui for interactive debugging
  - npm run test:playwright:report to view last report
  - Watch mode for component development
```

## Validation Loop

### Level 1: Syntax & Style

```bash
# Type check test files
npx tsc --noEmit tests/playwright/**/*.ts

# Lint test files
npm run lint tests/playwright

# Verify Playwright installation
npx playwright install --with-deps
```

### Level 2: Unit Tests

```bash
# Test contrast calculator with known values
npm run test:unit tests/playwright/utils/contrast-calculator.test.ts

# Test color extractor
npm run test:unit tests/playwright/utils/color-extractor.test.ts

# Expected: All utility functions pass unit tests
```

### Level 3: Integration Testing

```bash
# Run full Playwright test suite
npm run test:playwright

# Run contrast tests only
npm run test:playwright:contrast

# Run design pattern tests only
npm run test:playwright tests/playwright/specs/design-patterns

# Generate and view report
npm run test:playwright:report

# Expected: All tests pass, report generated successfully
```

### Level 4: Manual Validation

```bash
# Open Playwright UI for interactive debugging
npm run test:playwright:ui

# Verify report accuracy
# 1. Open generated HTML report
# 2. Check screenshot quality
# 3. Verify violation details match source code
# 4. Confirm recommendations are actionable

# Test against known violations
# 1. Temporarily introduce contrast violation
# 2. Run tests, verify detection
# 3. Revert change

# Expected: All violations detected, no false positives
```

## Final Validation Checklist

### Technical Validation

- [ ] Playwright installed and configured
- [ ] All test files pass TypeScript compilation
- [ ] Contrast calculator produces correct ratios for known color pairs
- [ ] Theme switching works correctly (verified via screenshots)
- [ ] All major components have test coverage
- [ ] Test suite completes in < 5 minutes

### Feature Validation

- [ ] Light mode contrast validated for all components
- [ ] Dark mode contrast validated for all components
- [ ] Design pattern violations detected accurately
- [ ] HTML report generated with all required sections
- [ ] Screenshots captured for both themes
- [ ] Violation locations include file paths and line numbers
- [ ] Recommendations are specific and actionable

### Code Quality Validation

- [ ] Test code follows TypeScript best practices
- [ ] Utilities are well-documented and reusable
- [ ] No hardcoded values (use constants/config)
- [ ] Error handling for edge cases
- [ ] Performance optimized (parallel test execution)

### Documentation & Deployment

- [ ] tests/README.md complete with examples
- [ ] CI/CD integration documented
- [ ] Pre-commit hook setup instructions provided
- [ ] Report interpretation guide included
- [ ] Troubleshooting section covers common issues

## Anti-Patterns to Avoid

- ❌ Don't test implementation details, test user-visible behavior
- ❌ Don't hardcode color values, use theme tokens
- ❌ Don't skip dark mode testing
- ❌ Don't ignore alpha transparency in contrast calculations
- ❌ Don't create brittle selectors (use data-testid attributes)
- ❌ Don't run all tests serially (use Playwright's parallel execution)
- ❌ Don't generate reports without screenshots
- ❌ Don't report violations without file locations
- ❌ Don't create false positives (validate detection logic thoroughly)
- ❌ Don't forget to test interactive states (hover, focus, active)

## Expected Outcomes

### Immediate Benefits
- Automated detection of contrast violations
- Enforcement of UI design standards
- Regression prevention for theme adaptation
- Objective quality metrics

### Long-term Benefits
- Reduced manual code review time
- Improved accessibility compliance
- Consistent design patterns across codebase
- Faster onboarding for new developers
- Confidence in refactoring efforts

### Sample Report Output

```
HACCP Helper - Playwright Test Report
Generated: 2025-01-05 17:45:00
Duration: 3m 42s

EXECUTIVE SUMMARY
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Overall Score: 87/100 (B+)
Components Tested: 24
Total Violations: 12
  ├─ Critical: 2
  ├─ High: 5
  └─ Medium: 5
Contrast Failures: 3

CRITICAL VIOLATIONS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
1. Dynamic Class Construction
   File: components/Dashboard.tsx:343
   Issue: className={`bg-${color}-500`}
   Fix: Use static lookup object from UI_STANDARDS.md Section 1

2. Missing Dark Mode Variant
   File: components/Dashboard.tsx:456
   Issue: bg-gray-50 without dark: variant
   Fix: Add dark:bg-gray-800 or use theme tokens

CONTRAST FAILURES (Dark Mode)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Dashboard > Filter Section > Label Text
  Ratio: 3.8:1 (Required: 4.5:1)
  Colors: #9ca3af on #1f2937
  Recommendation: Use theme.palette.text.secondary

[Full report continues with component-by-component breakdown...]
```

---

## Notes

- This PRP focuses on automated testing; manual accessibility audits still recommended
- Contrast validation is objective; usability testing still needed
- Design pattern checks catch common issues but don't replace code review
- Report should be reviewed regularly to track quality trends
- Consider adding performance testing in future iterations

<!-- EOF -->