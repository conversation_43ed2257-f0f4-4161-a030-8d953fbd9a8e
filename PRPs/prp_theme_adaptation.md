name: "PRP - Complete Dark/Light Theming Coverage"
description: "Plan to align all UI surfaces with the Material UI light/dark color schemes and remove hardcoded light-only styling."

---

## Original Story

Paste in the original story shared by the user below:

```
When the theme toggle is switched to dark mode only the shell updates; the dashboard cards, modals, and other components stay in the light palette. We need every surface to respect the selected theme.
```

## Story Metadata

**Story Type**: Bug  
**Estimated Complexity**: High  
**Primary Systems Affected**: `src/theme.ts`, `src/AppThemeProvider.tsx`, `components/*`, `tailwind.config.js`, global Tailwind + MUI integration

---

## Goal

**Feature Goal**: Every component and view inherits palette values from the Material UI theme so the light/dark toggle delivers a coherent visual system.  
**Deliverable**: PR updating theme tokens plus refactored React components that consume those tokens (no lingering hardcoded light colors).  
**Success Definition**: Toggling the `ModeToggle` button instantly restyles dashboard cards, modals, tables, and overlays without readability regressions; code search shows no hardcoded `#` colors or `bg-gray-*` Tailwind classes for semantic surfaces.

## User Persona (if applicable)

**Target User**: HACCP operations manager monitoring inventory/compliance dashboards.  
**Use Case**: Switching between light and dark mode while reviewing data across dashboard, inventory, logs, and modal workflows.  
**User Journey**: User authenticates, lands on Dashboard, toggles theme in the top bar, navigates through Inventory, Orders, Vendors, Calendar, opens import/email modals, and expects consistent theming everywhere.  
**Pain Points Addressed**: Eye strain and inconsistent contrast in dark mode, visual bugs when presenting dashboards on shared monitors, maintenance headache from duplicated color definitions.

## Why

- Ensures accessibility and readability standards across both color schemes, avoiding partial dark mode.  
- Aligns Tailwind-heavy components with the Material UI color system already powering the layout shell.  
- Reduces future maintenance by centralizing tokens instead of scattering light-only colors across components.

## What

Refactor Tailwind-styled feature components to consume Material UI palette variables (or shared semantic tokens), extend the theme with domain-specific colors (event types, status chips), and audit all overlays/modals to adopt theme-aware surfaces, text, borders, and focus states. Remove reliance on Tailwind’s default light palette where `dark:` variants cannot trigger because we toggle color scheme via MUI.

### Success Criteria

- [ ] Mode toggle restyles Dashboard cards, KPI chips, charts, and filters (`components/Dashboard.tsx`).  
- [ ] Inventory, Orders, Vendors, Calendar, Temperature, and Lot Tracking views read colors from theme tokens rather than Tailwind grays.  
- [ ] Modals/overlays (Import, Email Draft, StatusIndicator, ConversationLog) inherit `background.paper`, typography, and divider colors from theme.  
- [ ] Search `rg "bg-gray" components` and `rg "#[0-9a-fA-F]{3,6}" components` returns only intentional icon palette constants or theme definitions.  
- [ ] Manual QA across light/dark confirms WCAG AA contrast for headlines, body copy, and interactive controls.

## All Needed Context

### Context Completeness Check

The linked files show how the theme toggle currently works (`AppThemeProvider` + `theme.ts`), where Tailwind classes hardcode light colors (Dashboard, InventoryDisplay, ImportModal, etc.), and the UI standards doc capturing theming rules. This PRP documents all affected surfaces and the migration path so an engineer new to the repo can execute without reverse-engineering the theme system.

### Documentation & References

```yaml
- file: src/theme.ts
  why: Defines light/dark palette, css variables, and component overrides; extend here with semantic tokens.
  pattern: `berry` palette object + `components.MuiPaper` overrides use `var(--mui-palette-divider)`.
  gotcha: Dark scheme uses near-black surfaces (`#0b0f16` / `#0f131b`); ensure derived colors maintain contrast.

- file: src/AppThemeProvider.tsx
  why: Hosts `ModeToggle` (`useColorScheme`) and injects `ThemeProvider`.
  pattern: Uses `ThemeProvider` with `defaultMode="light"` and `CssBaseline`.
  gotcha: No persistence (localStorage) yet—don’t break existing toggle semantics.

- file: src/layouts/DashboardLayout.tsx
  why: Example of theme-aware surfaces using `sx` (`bgcolor: 'background.default'`, `borderColor: 'divider'`).
  pattern: Reuse this approach for feature-level layouts.
  gotcha: Drawer/AppBar rely on theme transitions; keep structural styles intact.

- file: components/Dashboard.tsx
  why: Tailwind classes (`bg-gray-50`, `text-gray-800`, dynamic `bg-${tailwindColor}`) ignore dark mode.
  pattern: Replace with MUI primitives (`Paper`, `Chip`, `Box`) and theme palette tokens.
  gotcha: Event type color lookup must expose semantic colors for both schemes (move to shared map).

- file: components/InventoryDisplay.tsx
  why: Uses Tailwind grays/blue for cards and typography.
  pattern: Convert to `Paper` + `Typography` with theme tokens.
  gotcha: Keep grid responsiveness while switching to MUI `Grid`/`Box`.

- file: components/ImportModal.tsx
  why: Imports rely on dark-tailored Tailwind backgrounds (`bg-gray-800`) that clash in light mode.
  pattern: Reuse theme `palette.background` and `palette.grey` scales.
  gotcha: TextField replacements must preserve current validation indicators.

- file: components/StatusIndicator.tsx
  why: Overlay uses fixed semi-transparent dark background.
  pattern: Map to `alpha(theme.palette.background.paper, …)` instead of literal values.
  gotcha: Keep blur/backdrop interplay.

- file: components/ConversationLog.tsx
  why: Tailwind backgrounds for chat bubbles ignore theme.
  pattern: Adopt `Paper` / `Box` with `color` + `bgcolor` from palette.
  gotcha: Maintain reverse column order and scroll behavior.

- file: tailwind.config.js
  why: Shows Tailwind still enabled; dark mode defaults to `media`.
  pattern: Document why we cannot rely on `dark:` utilities given MUI’s class-based toggle.
  gotcha: Avoid conflicting dark-mode providers; consider setting `darkMode: ['class', '[data-mui-color-scheme="dark"]']` only if essential.

- docfile: PRPs/ai_docs/UI_STANDARDS.md
  why: Section 3 enforces dual-theme coverage; reference for automated checks.
  section: "3. THEMING"
```

### Current Codebase tree (run `tree` in the root of the project) to get an overview of the codebase

```bash
.
├── App.tsx
├── components/
│   ├── Dashboard.tsx
│   ├── InventoryDisplay.tsx
│   ├── ImportModal.tsx
│   ├── ConversationLog.tsx
│   ├── StatusIndicator.tsx
│   ├── OrdersView.tsx
│   ├── VendorsView.tsx
│   ├── TemperatureView.tsx
│   ├── CalendarView.tsx
│   ├── LotTracking/
│   │   └── MovementHistory.tsx
│   └── ...
├── src/
│   ├── AppThemeProvider.tsx
│   ├── layouts/
│   │   └── DashboardLayout.tsx
│   └── theme.ts
├── tailwind.config.js
└── index.css
```

### Desired Codebase tree with files to be added and responsibility of file

```bash
.
├── src/
│   ├── theme.ts                          # Extended palette with semantic tokens & module augmentation
│   ├── theme/
│   │   └── tokens.ts                     # NEW: domain color maps (event types, status chips, semantic surfaces)
│   └── AppThemeProvider.tsx              # Updated to expose mode + optional persistence helpers
├── components/
│   ├── common/
│   │   └── SurfaceCard.tsx               # NEW: shared Paper wrapper with consistent padding/background
│   ├── Dashboard.tsx                     # Refactored to use theme tokens + SurfaceCard
│   ├── InventoryDisplay.tsx              # Refactored to theme tokens + responsive MUI layout
│   ├── ImportModal.tsx                   # Theme-aware surfaces/inputs
│   ├── ConversationLog.tsx               # Theme-aware bubbles
│   ├── StatusIndicator.tsx               # Theme-aware overlay
│   ├── OrdersView.tsx | VendorsView.tsx | TemperatureView.tsx | CalendarView.tsx | LotTracking/*  # All updated to palette tokens
```

### Known Gotchas of our codebase & Library Quirks

```python
# CRITICAL: Tailwind dark mode defaults to media—MUI toggle adds `data-mui-color-scheme`, not `dark` class.
# CRITICAL: MUI theme CSS variables accessible via `theme.vars.palette`; avoid direct `theme.palette` when animating transitions.
# CRITICAL: Many components mix Tailwind + MUI; ensure we do not double-style borders/shadows causing layout shifts.
# CRITICAL: Dynamic Tailwind classes (e.g., bg-${tailwindColor}) break purge; migrate to semantic maps within TS.
```

## Implementation Blueprint

### Data models and structure

- Introduce `src/theme/tokens.ts` exporting semantic color maps (`surface`, `muted`, `accent`, `eventTypePalette`) keyed by theme mode.  
- Augment `Theme` interface so components can access `theme.palette.semantic.success`, `theme.palette.semantic.surfaceElevated`, etc.  
- Provide helper `getEventTypeChip(theme, eventType)` returning `{ iconColor, chipColor, textColor }`.

### Implementation Tasks (ordered by dependencies)

```yaml
### CREATE src/theme/tokens.ts:
- IMPLEMENT: Export `semanticSurfaces`, `textContrasts`, `eventTypePalette` keyed by `'light' | 'dark'`.
- PATTERN: Follow `berry` object organization in src/theme.ts for nested scales.
- IMPORTS: `PaletteColorOptions` from '@mui/material/styles'.
- GOTCHA: Ensure color values meet contrast ratio > 4.5 against backgrounds (use existing berry neutrals).
- **VALIDATE**: `npx tsc --noEmit`

### UPDATE src/theme.ts:
- IMPLEMENT: Import tokens, extend `createTheme` call to merge `palette.semantic`, `palette.eventTypes`.
- PATTERN: Use `theme.getColorSchemeSelector` docs for cssVariables; mimic `MuiPaper` overrides with `var()` usage.
- IMPORTS: New token exports, `PaletteOptions`.
- GOTCHA: Augment `Theme`/`Palette` types via module augmentation so TypeScript allows `theme.palette.semantic`.
- **VALIDATE**: `npx tsc --noEmit`

### UPDATE src/AppThemeProvider.tsx:
- IMPLEMENT: Expose `ModeToggle` plus optional persisted mode (e.g., localStorage) without breaking current behavior.
- PATTERN: Use `useEffect` + `useColorScheme` similar to MUI docs.
- IMPORTS: `useEffect`.
- GOTCHA: Guard for SSR/Window before touching localStorage.
- **VALIDATE**: `npm run build`

### CREATE components/common/SurfaceCard.tsx:
- IMPLEMENT: Wrapper around `Paper` applying padding, gap, and optional `variant="outlined"` with theme-aware hover styles.
- PATTERN: Mirror existing card spacing in Dashboard (p-4, rounded).
- IMPORTS: `Paper`, `Box`, `useTheme`.
- GOTCHA: Allow `component` prop pass-through for semantic usage (e.g., `component="section"`).
- **VALIDATE**: `npm run build`

### UPDATE components/Dashboard.tsx:
- IMPLEMENT: Replace Tailwind classes with `SurfaceCard`, `Stack`, `Grid`, `Chip`, and theme-based colors for KPIs and charts.
- PATTERN: Use `DashboardLayout` + new token helpers for event type chips and charts.
- IMPORTS: `Box`, `Grid`, `Typography`, `Chip`, `alpha`, helper from `theme/tokens`.
- GOTCHA: Preserve memoized calculations; ensure SVG segments use theme palette.
- **VALIDATE**: `npm run build`

### UPDATE components/InventoryDisplay.tsx:
- IMPLEMENT: Convert to `SurfaceCard` grid, using `theme.palette.primary` for quantities and `text.secondary`.
- PATTERN: Align card spacing with Dashboard.
- IMPORTS: `Grid`, `Typography`, `SurfaceCard`.
- GOTCHA: Maintain responsive layout (sm/md/lg column counts).
- **VALIDATE**: `npm run build`

### UPDATE components/ImportModal.tsx & dependent subcomponents:
- IMPLEMENT: Migrate to MUI form controls (`TextField`, `Box`, `Stack`) with theme colors; keep highlight states using `palette.success`.
- PATTERN: Reuse `Dialog` overrides from theme.
- IMPORTS: `Paper`, `TextField`, `Chip`, `SurfaceCard`.
- GOTCHA: Performance when rendering large import lists—avoid re-render heavy stylings.
- **VALIDATE**: `npm run build`

### UPDATE components/StatusIndicator.tsx & components/ConversationLog.tsx:
- IMPLEMENT: Replace Tailwind overlays with theme-aware `Paper`, `alpha(theme.palette.background.default, .6)` backgrounds.
- PATTERN: Reference `App.tsx` overlay usage.
- IMPORTS: `Paper`, `useTheme`, `alpha`.
- GOTCHA: Ensure z-index/backdrop align with voice assistant controls.
- **VALIDATE**: `npm run build`

### UPDATE OrdersView.tsx, VendorsView.tsx, TemperatureView.tsx, CalendarView.tsx, LotTracking/*.tsx:
- IMPLEMENT: Sweep for hardcoded Tailwind grays/blues; replace with theme tokens or `SurfaceCard`.
- PATTERN: Apply consistent typography tokens from theme (use `Typography` variants).
- IMPORTS: `SurfaceCard`, `Box`, `Typography`, theme helpers.
- GOTCHA: Preserve form validations and focus styling (use `theme.vars.focusVisible` from `MuiCssBaseline` override).
- **VALIDATE**: `rg "bg-gray" components && rg "#[0-9a-fA-F]{3,6}" components`

### UPDATE tailwind.config.js (optional alignment):
- IMPLEMENT: Document (or configure) darkMode to listen to `[data-mui-color-scheme="dark"]` if residual utilities remain.
- PATTERN: `darkMode: ['class', '[data-mui-color-scheme="dark"]']`
- GOTCHA: If leaving Tailwind for structural utilities only, ensure tree-shaking still keeps classes used via `clsx`.
- **VALIDATE**: `npm run build`

### QA PASS:
- IMPLEMENT: Manual regression—toggle theme, navigate Dashboard, Inventory, Vendors, Orders, Calendar, open Import/Email modals, trigger Lot Tracking overlay.
- PATTERN: Follow manual validation script in PR summary.
- GOTCHA: Confirm voice assistant overlay and microphone button still pass contrast requirements.
- **VALIDATE**: `npm run dev` (manual), `npx tsc --noEmit`, `npm run build`
```

## Validation Loop

### Level 1: Syntax & Style (Immediate Feedback)

```bash
npx tsc --noEmit
npm run lint  # if configured; otherwise run `npm run build` for vite linting feedback
npm run format -- if repository provides formatter
```

### Level 2: Unit Tests (Component Validation)

_No automated tests exist yet._ Create targeted Storybook/Chromatic snapshots or leverage visual regression tooling if available. Otherwise rely on manual component testing via Vite dev server.

### Level 3: Integration Testing (System Validation)

```bash
npm run dev  # Manually flip theme, traverse all pages, capture before/after screenshots
npm run build && npm run preview  # Ensure production bundle renders with both color schemes
```

### Level 4: Creative & Domain-Specific Validation

- Capture light/dark screenshots of Dashboard + Modals for PR attachments.  
- Optional: Run accessibility contrast check (e.g., Lighthouse, axe) focusing on dark mode surfaces.  
- Validate voice session overlay readability while assistant is streaming audio.

## Final Validation Checklist

### Technical Validation

- [ ] `npx tsc --noEmit` succeeds  
- [ ] `npm run build` passes without warnings  
- [ ] No `rg "bg-gray"` or raw hex matches outside sanctioned theme files

### Feature Validation

- [ ] Theme toggle updates every high-traffic view and modal  
- [ ] Manual QA confirms acceptable contrast ratios in both modes  
- [ ] Screenshots for PR show parity

### Code Quality Validation

- [ ] Components use shared `SurfaceCard` and theme tokens instead of bespoke styling  
- [ ] Module augmentation documented for custom palette fields  
- [ ] Tailwind usage limited to layout utilities (margins, flex) or reconfigured with dark selector awareness

### Documentation & Deployment

- [ ] PR body lists manual validation steps + screenshot comparisons  
- [ ] New theme tokens explained inline (comments or README snippet)

## Notes

- Coordinate with design on final palette choices before locking tokens.  
- Consider adding automated UI regression testing (Chromatic/Playwright) in follow-up work.  
- If future components still need Tailwind variants, revisit tailwind.config.js to align with `data-mui-color-scheme` toggle.

<!-- EOF -->
