# Voice Integration Overview

This document captures the current Google Gemini–powered voice workflow embedded in the Vite/React application. It highlights the moving parts so the feature can be maintained, extended, or duplicated for other domains.

## Implementation Summary

- The voice assistant lives inside `App.tsx`, beginning with the `systemInstruction` template and `functionDeclarations` memo (App.tsx:677, App.tsx:690). These declarations are the contract that Gemini Live uses to decide when to call back into app logic.
- Audio marshalling utilities—`encode`, `decode`, `decodeAudioData`, and `createBlob`—live in `utils/audioUtils.ts` and convert between browser audio buffers and the PCM blob format expected by Gemini (utils/audioUtils.ts:5, utils/audioUtils.ts:27, utils/audioUtils.ts:42, utils/audioUtils.ts:57).
- UI feedback is handled by `StatusIndicator` (components/StatusIndicator.tsx) and `ConversationLog` (components/ConversationLog.tsx), which render the assistant status and transcript history.
- Firebase-backed data is kept in sync through multiple snapshot listeners in `App.tsx`, populating local state that the voice functions read or mutate (App.tsx:286-318).

## Runtime Flow

1. **API key gating** – On boot, `checkApiKey` probes the `window.aistudio` bridge to confirm a Gemini API key is selected; otherwise a blocking dialog prompts users to pick one (App.tsx:274-284, App.tsx:1651-1675).
2. **Session start (`startSession`)** – Triggered by the floating MUI FAB, the handler:
   - Clears prior transcripts, checks `apiKeySelected`, and instantiates the Gemini client with `import.meta.env.VITE_GEMINI_API_KEY` (App.tsx:924-937).
   - Creates separate input/output `AudioContext` objects and requests a microphone stream via `navigator.mediaDevices.getUserMedia` (App.tsx:938-942).
   - Calls `ai.live.connect` with model `gemini-2.5-flash-native-audio-preview-09-2025`, wiring `onopen`, `onmessage`, `onerror`, and `onclose` callbacks plus the tool/function manifest (App.tsx:944-1063).
3. **Streaming microphone audio** – Inside `onopen`, the browser stream pipes through a `ScriptProcessorNode`; every chunk is converted to a PCM blob via `createBlob` and forwarded to the live session (`session.sendRealtimeInput`) (App.tsx:951-961, utils/audioUtils.ts:57).
4. **Handling server messages** – `onmessage` performs four jobs (App.tsx:966-1039):
   - Accumulates interim input/output transcripts until Gemini marks a turn complete, then appends them to React state via `createConversationTurn`.
   - Responds to tool calls by delegating through `processFunctionCall`, relaying both a natural-language confirmation and a structured `functionResponses` payload back to Gemini (App.tsx:889-921, App.tsx:990-1003).
   - Plays synthesized speech by base64-decoding inline audio data, scheduling it in the output `AudioContext`, and tracking active sources for interruption control (App.tsx:1007-1038, utils/audioUtils.ts:27, utils/audioUtils.ts:42).
   - Honours `interrupted` flags by halting any queued audio to keep playback in sync.
5. **Error and teardown handling** – `onerror` resets UI state and forces API key re-selection when Gemini reports missing entities, and `onclose`/`stopSession` clean up streams, audio nodes, and status flags (App.tsx:1041-1093).

## Voice-Accessible Functions

Each tool declaration maps 1:1 to an implementation inside `App.tsx`. The assistant confirms ambiguous inputs before invoking them per the `systemInstruction`.

- **`log_haccp_event`** – Adds an event document to Firestore, stamping defaults for date, time, author, and unit where applicable (App.tsx:787-789). Relies on the dynamic enum lists drawn from Firestore snapshots to keep Gemini prompts anchored to live domain data.
- **`get_inventory_summary`** – Summarises current inventory by product using the `inventory` memo, which replays event history to accumulate net weights (App.tsx:792-797, App.tsx:156-193).
- **`check_stock_at_location`** – Reads the `inventoryByLocation` memo to report per-location stock or indicate emptiness (App.tsx:799-808, App.tsx:195-247).
- **`create_purchase_order`** – Validates the vendor against the `vendors` collection, then writes a planned purchase order with server timestamps (App.tsx:811-828).
- **`find_events`** – Filters the locally cached `events` array by various criteria, returning a human-readable summary plus event IDs for follow-up actions (App.tsx:830-848).
- **`update_event`** – Applies targeted updates to an existing event document, ensuring only provided fields change and timestamping the modification (App.tsx:851-913).

The `processFunctionCall` dispatcher keeps the LLM-facing surface area contained and guarantees that every tool reply is mirrored into the conversation log before acknowledging Gemini (App.tsx:889-921, App.tsx:990-1003).

## UI Surface

- The floating action button toggles `startSession`/`stopSession`, exposing real-time status text and a minimal chat log when active (App.tsx:1588-1669).
- `StatusIndicator` shows a pulsing badge while the microphone is streaming (components/StatusIndicator.tsx:6-18).
- `ConversationLog` renders the turn history in reverse chronological order for immediate contextual recall during a session (components/ConversationLog.tsx:8-33).

## Duplication & Extension Ideas

1. **Extract a reusable hook** – Factor the session lifecycle (`startSession`, `stopSession`, refs, and `processFunctionCall`) into `useGeminiVoiceSession`. Consumers could inject `systemInstruction`, `functionDeclarations`, and callback handlers, enabling other views to mount independent assistants without duplicating audio plumbing.
2. **Add domain-specific tools** – To support new workflows (e.g., lot tracking or temperature monitoring), extend the `functionDeclarations` memo with additional entries and implement the matching cases in `processFunctionCall`. The dynamic enum strings already scaffold the prompts with live Firestore values.
3. **Support multi-modal sessions** – The current `responseModalities` request only audio. Duplicated implementations could request `TEXT` or `JSON` outputs alongside audio for richer UI updates, or speculate about using `CameraModal`’s upload pipeline to add `media` parts mid-session.
4. **Isolate UI chrome** – Wrap the status panel and log into a `<VoiceSessionOverlay>` component so other routes or micro-frontends can reuse the same conversational HUD by simply passing transcript data and status flags.

## Configuration Notes

- Requires `@google/genai` with a valid Gemini API key surfaced as `VITE_GEMINI_API_KEY` in the Vite environment (App.tsx:936, App.tsx:1062).
- Browser access to `navigator.mediaDevices.getUserMedia({ audio: true })` must be granted for live sessions to start (App.tsx:941-942).
- The optional `window.aistudio` bridge simplifies key selection when the app is launched inside Google’s AI Studio wrapper; when absent, the code assumes the env var is already present (App.tsx:274-284, App.tsx:592-597).

With these components in mind, the current voice stack can be ported or enhanced while keeping parity with Gemini’s live tool-calling behaviour.
