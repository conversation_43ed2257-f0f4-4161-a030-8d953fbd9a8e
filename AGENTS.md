# Repository Guidelines

## Project Structure & Module Organization
The Vite entrypoint lives in `index.tsx` and renders `App.tsx`, which composes feature views from `components/`. Group feature code with its supporting hooks and subcomponents inside the existing domain folders (for example, `components/LotTracking/`). Shared data helpers reside in `utils/`, Firebase adapters in `firebase/`, and service integrations (Temp Stick, email, etc.) in `services/`. Keep generated assets in `dist/` and seed/reference files in `data/` and `PRPs/`. Define shared types in `types.ts` so UI layers consume a single source of truth.

## Build, Test, and Development Commands
Install dependencies with `pnpm install` (preferred) or `npm install`. Use `pnpm dev` for the Vite dev server with hot reloading, and `pnpm build` to produce the production bundle in `dist/`. Preview the built assets locally via `pnpm preview`. Type-safety is enforced by running `pnpm exec tsc --noEmit`, especially before commits that touch shared models or service contracts.

## Coding Style & Naming Conventions
Code is authored in TypeScript with React 19 function components. Follow the prevailing two-space indentation, single quotes, and trailing commas for multi-line literals. Name components and contexts in `PascalCase`, hooks and helpers in `camelCase`, and exported type definitions in `PascalCase` within `types.ts`. Co-locate feature-specific styles or constants beside the component that uses them, but keep cross-cutting utilities in `utils/`. Prefer named exports unless a module exposes a single primary component.

## Testing Guidelines
Automated tests are not configured yet; when adding them, use Vitest (Vite-compatible) and React Testing Library. Store specs alongside the component in `components/<Feature>/__tests__/ComponentName.test.tsx`. Name scenario-based tests after the user story they protect. Until coverage is in place, document manual validation steps in pull requests (e.g., “Verified lot movement updates Firestore and refreshes the dashboard”).

## Commit & Pull Request Guidelines
Follow the existing conventional commit style (`feat:`, `fix:`, `refactor:`, etc.), e.g., `feat: add freezer layout modal controls`. Keep commits focused on a single concern and include type-check/build results in the description if the change is build-sensitive. Pull requests should summarize the problem, outline the solution, link any related issue, and attach UI screenshots or Loom clips for visual changes. Note required environment variables or migration steps in the PR body.

## Environment & Security Notes
Firebase credentials currently live in `firebase/config.ts`. When rotating keys, prefer `.env.local` with `import.meta.env` and update the config to read from environment variables. Never commit service account JSON or production data; scrub example spreadsheets in `data/` before sharing externally.
